package org.dromara.app.domain.bo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 题目评论业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QuestionCommentBo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 评论ID
     */
    @NotNull(message = "评论ID不能为空", groups = {EditGroup.class})
    private Long commentId;

    /**
     * 题目ID
     */
    @NotNull(message = "题目ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long questionId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 父评论ID（回复时使用）
     */
    private Long parentId;

    /**
     * 评论内容
     */
    @NotBlank(message = "评论内容不能为空", groups = {AddGroup.class, EditGroup.class})
    private String content;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 回复数
     */
    private Integer replyCount;

    /**
     * 状态（0正常 1删除）
     */
    private String status;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 备注
     */
    private String remark;
}
