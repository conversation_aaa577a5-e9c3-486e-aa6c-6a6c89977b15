package org.dromara.app.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.app.domain.QuestionBank;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T15:35:30+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.10 (Oracle Corporation)"
)
@Component
public class QuestionBankBoToQuestionBankMapperImpl implements QuestionBankBoToQuestionBankMapper {

    @Override
    public QuestionBank convert(QuestionBankBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        QuestionBank questionBank = new QuestionBank();

        questionBank.setSearchValue( arg0.getSearchValue() );
        questionBank.setCreateDept( arg0.getCreateDept() );
        questionBank.setCreateBy( arg0.getCreateBy() );
        questionBank.setCreateTime( arg0.getCreateTime() );
        questionBank.setUpdateBy( arg0.getUpdateBy() );
        questionBank.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            questionBank.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        questionBank.setBankId( arg0.getBankId() );
        questionBank.setBankCode( arg0.getBankCode() );
        questionBank.setTitle( arg0.getTitle() );
        questionBank.setDescription( arg0.getDescription() );
        questionBank.setMajorId( arg0.getMajorId() );
        questionBank.setIcon( arg0.getIcon() );
        questionBank.setColor( arg0.getColor() );
        questionBank.setDifficulty( arg0.getDifficulty() );
        questionBank.setTotalQuestions( arg0.getTotalQuestions() );
        questionBank.setPracticeCount( arg0.getPracticeCount() );
        questionBank.setCategories( arg0.getCategories() );
        questionBank.setSort( arg0.getSort() );
        questionBank.setStatus( arg0.getStatus() );
        questionBank.setRemark( arg0.getRemark() );

        return questionBank;
    }

    @Override
    public QuestionBank convert(QuestionBankBo arg0, QuestionBank arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setBankId( arg0.getBankId() );
        arg1.setBankCode( arg0.getBankCode() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setDescription( arg0.getDescription() );
        arg1.setMajorId( arg0.getMajorId() );
        arg1.setIcon( arg0.getIcon() );
        arg1.setColor( arg0.getColor() );
        arg1.setDifficulty( arg0.getDifficulty() );
        arg1.setTotalQuestions( arg0.getTotalQuestions() );
        arg1.setPracticeCount( arg0.getPracticeCount() );
        arg1.setCategories( arg0.getCategories() );
        arg1.setSort( arg0.getSort() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setRemark( arg0.getRemark() );

        return arg1;
    }
}
