{"doc": " 面试问题管理控制器\n\n <AUTHOR> Assistant\n @date 2025-07-19\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.Long", "java.lang.String", "java.lang.Integer", "java.lang.String"], "doc": " 分页查询问题列表\n"}, {"name": "getQuestionsByDomain", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": " 根据技术领域查询问题\n"}, {"name": "getMultimodalQuestions", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 获取多模态问题\n"}, {"name": "getGradedQuestions", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer", "java.lang.Integer"], "doc": " 根据难度分级获取问题\n"}, {"name": "getQuestionsByTags", "paramTypes": ["java.util.List", "java.lang.Integer"], "doc": " 根据标签查询问题\n"}, {"name": "getTags", "paramTypes": ["java.lang.String"], "doc": " 获取问题标签\n"}, {"name": "getHotTags", "paramTypes": ["java.lang.Integer"], "doc": " 获取热门标签\n"}, {"name": "getTagsByCategory", "paramTypes": [], "doc": " 获取所有分类的标签\n"}, {"name": "getDifficultySuggestion", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 获取问题难度分级建议\n"}, {"name": "getQuestionTypeStats", "paramTypes": ["java.lang.String"], "doc": " 获取问题类型统计\n"}, {"name": "calculateDifficultyDistribution", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 计算难度分布建议\n"}, {"name": "getQuestionTypeStatistics", "paramTypes": ["java.lang.String"], "doc": " 获取问题类型统计\n"}], "constructors": []}