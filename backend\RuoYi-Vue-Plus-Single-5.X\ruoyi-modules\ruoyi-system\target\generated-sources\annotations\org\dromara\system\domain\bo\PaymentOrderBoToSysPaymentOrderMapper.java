package org.dromara.system.domain.bo;

import io.github.linpeilie.AutoMapperConfig__104;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysPaymentOrder;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__104.class,
    uses = {},
    imports = {}
)
public interface PaymentOrderBoToSysPaymentOrderMapper extends BaseMapper<PaymentOrderBo, SysPaymentOrder> {
}
