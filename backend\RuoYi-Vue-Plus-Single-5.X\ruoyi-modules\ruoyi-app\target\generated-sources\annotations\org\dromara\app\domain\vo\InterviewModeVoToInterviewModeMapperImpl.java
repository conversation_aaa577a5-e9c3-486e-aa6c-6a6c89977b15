package org.dromara.app.domain.vo;

import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.annotation.processing.Generated;
import org.dromara.app.domain.InterviewMode;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T15:35:30+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.10 (Oracle Corporation)"
)
@Component
public class InterviewModeVoToInterviewModeMapperImpl implements InterviewModeVoToInterviewModeMapper {

    @Override
    public InterviewMode convert(InterviewModeVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        InterviewMode interviewMode = new InterviewMode();

        if ( arg0.getCreateTime() != null ) {
            interviewMode.setCreateTime( Date.from( arg0.getCreateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        if ( arg0.getUpdateTime() != null ) {
            interviewMode.setUpdateTime( Date.from( arg0.getUpdateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        interviewMode.setId( arg0.getId() );
        interviewMode.setName( arg0.getName() );
        interviewMode.setDescription( arg0.getDescription() );
        interviewMode.setIcon( arg0.getIcon() );
        interviewMode.setColor( arg0.getColor() );
        interviewMode.setDuration( arg0.getDuration() );
        interviewMode.setDifficulty( arg0.getDifficulty() );
        List<String> list = arg0.getFeatures();
        if ( list != null ) {
            interviewMode.setFeatures( new ArrayList<String>( list ) );
        }
        interviewMode.setSortOrder( arg0.getSortOrder() );
        interviewMode.setStatus( arg0.getStatus() );

        return interviewMode;
    }

    @Override
    public InterviewMode convert(InterviewModeVo arg0, InterviewMode arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        if ( arg0.getCreateTime() != null ) {
            arg1.setCreateTime( Date.from( arg0.getCreateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        else {
            arg1.setCreateTime( null );
        }
        if ( arg0.getUpdateTime() != null ) {
            arg1.setUpdateTime( Date.from( arg0.getUpdateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        else {
            arg1.setUpdateTime( null );
        }
        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setDescription( arg0.getDescription() );
        arg1.setIcon( arg0.getIcon() );
        arg1.setColor( arg0.getColor() );
        arg1.setDuration( arg0.getDuration() );
        arg1.setDifficulty( arg0.getDifficulty() );
        if ( arg1.getFeatures() != null ) {
            List<String> list = arg0.getFeatures();
            if ( list != null ) {
                arg1.getFeatures().clear();
                arg1.getFeatures().addAll( list );
            }
            else {
                arg1.setFeatures( null );
            }
        }
        else {
            List<String> list = arg0.getFeatures();
            if ( list != null ) {
                arg1.setFeatures( new ArrayList<String>( list ) );
            }
        }
        arg1.setSortOrder( arg0.getSortOrder() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
