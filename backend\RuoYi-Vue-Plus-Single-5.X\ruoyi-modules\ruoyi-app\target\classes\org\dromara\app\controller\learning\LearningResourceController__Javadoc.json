{"doc": " 学习资源控制器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "createResource", "paramTypes": ["org.dromara.app.domain.LearningResource"], "doc": " 创建学习资源\n\n @param resource 学习资源\n @return 操作结果\n"}, {"name": "updateResource", "paramTypes": ["org.dromara.app.domain.LearningResource"], "doc": " 更新学习资源\n\n @param resource 学习资源\n @return 操作结果\n"}, {"name": "deleteResource", "paramTypes": ["java.lang.Long"], "doc": " 删除学习资源\n\n @param resourceId 资源ID\n @return 操作结果\n"}, {"name": "getResource", "paramTypes": ["java.lang.Long"], "doc": " 获取资源详情\n\n @param resourceId 资源ID\n @return 学习资源\n"}, {"name": "getResourcesPage", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 分页查询资源\n\n @param pageNum 页码\n @param pageSize 每页大小\n @param skillArea 技能领域\n @param type 资源类型\n @param difficulty 难度等级\n @return 分页结果\n"}, {"name": "getRecommendedResources", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": " 获取推荐资源\n\n @param skillArea 技能领域\n @param difficulty 难度等级\n @param limit 限制数量\n @return 资源列表\n"}, {"name": "getResourcesByType", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 根据类型获取资源\n\n @param type 资源类型\n @param limit 限制数量\n @return 资源列表\n"}, {"name": "getPopularResources", "paramTypes": ["java.lang.Integer"], "doc": " 获取热门资源\n\n @param limit 限制数量\n @return 资源列表\n"}, {"name": "getFreeResources", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 获取免费资源\n\n @param skillArea 技能领域\n @param limit 限制数量\n @return 资源列表\n"}, {"name": "getResourcesByTag", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 根据标签获取资源\n\n @param tag 标签\n @param limit 限制数量\n @return 资源列表\n"}, {"name": "searchResources", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 搜索资源\n\n @param keyword 关键词\n @param limit 限制数量\n @return 资源列表\n"}, {"name": "assessResourceQuality", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.LearningResource.QualityAssessment"], "doc": " 评估资源质量\n\n @param resourceId 资源ID\n @param qualityAssessment 质量评估\n @return 操作结果\n"}, {"name": "updateResourceRating", "paramTypes": ["java.lang.Long", "java.lang.Double"], "doc": " 更新资源评分\n\n @param resourceId 资源ID\n @param rating 评分\n @return 操作结果\n"}, {"name": "startLearning", "paramTypes": ["java.lang.Long"], "doc": " 开始学习资源\n\n @param resourceId 资源ID\n @return 操作结果\n"}, {"name": "completeLearning", "paramTypes": ["java.lang.Long"], "doc": " 完成学习资源\n\n @param resourceId 资源ID\n @return 操作结果\n"}, {"name": "getSimilarResources", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 获取相似资源\n\n @param resourceId 资源ID\n @param limit 限制数量\n @return 资源列表\n"}, {"name": "performQualityAssessment", "paramTypes": ["java.lang.Long"], "doc": " 执行质量评估\n\n @param resourceId 资源ID\n @return 质量评估结果\n"}, {"name": "batchImportResources", "paramTypes": ["java.util.List"], "doc": " 批量导入资源\n\n @param resources 资源列表\n @return 导入结果\n"}, {"name": "getResourceStatistics", "paramTypes": [], "doc": " 获取资源统计信息\n\n @return 统计信息\n"}, {"name": "getSkillAreaDistribution", "paramTypes": [], "doc": " 获取技能领域分布\n\n @return 技能领域分布\n"}, {"name": "getResourceTypeDistribution", "paramTypes": [], "doc": " 获取资源类型分布\n\n @return 资源类型分布\n"}, {"name": "getTopProviders", "paramTypes": ["java.lang.Integer"], "doc": " 获取顶级提供者\n\n @param limit 限制数量\n @return 提供者排行\n"}, {"name": "getResourceTagCloud", "paramTypes": ["java.lang.Integer"], "doc": " 获取资源标签云\n\n @param limit 限制数量\n @return 标签统计\n"}, {"name": "getPersonalizedRecommendations", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 获取个性化推荐资源\n\n @param userId 用户ID\n @param limit 限制数量\n @return 个性化推荐资源列表\n"}, {"name": "getResourcesBySkillGaps", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": " 根据技能差距推荐资源\n\n @param skillAreas 技能领域列表（逗号分隔）\n @param difficulty 难度等级\n @param limit 限制数量\n @return 推荐资源列表\n"}, {"name": "getCollaborativeFilteringRecommendations", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 获取协同过滤推荐资源\n\n @param userId 用户ID\n @param limit 限制数量\n @return 推荐资源列表\n"}, {"name": "getTrendingResources", "paramTypes": ["java.lang.Integer", "java.lang.Integer"], "doc": " 获取趋势资源\n\n @param days 天数\n @param limit 限制数量\n @return 趋势资源列表\n"}, {"name": "sortResourcesByRecommendation", "paramTypes": ["java.util.List", "java.util.Map"], "doc": " 智能推荐资源排序\n\n @param resources 原始资源列表\n @param userPreferences 用户偏好\n @return 排序后的资源列表\n"}], "constructors": []}