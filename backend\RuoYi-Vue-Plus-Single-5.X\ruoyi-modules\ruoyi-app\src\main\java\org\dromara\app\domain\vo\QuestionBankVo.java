package org.dromara.app.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 题库视图对象
 *
 * <AUTHOR>
 */
@Data
public class QuestionBankVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 题库ID
     */
    private String id;

    /**
     * 题库编码
     */
    private String bankCode;

    /**
     * 题库标题
     */
    private String title;

    /**
     * 题库描述
     */
    private String description;

    /**
     * 题库图标
     */
    private String icon;

    /**
     * 题库颜色
     */
    private String color;

    /**
     * 难度
     */
    private String difficulty;

    /**
     * 题目总数
     */
    private Integer totalQuestions;

    /**
     * 练习次数
     */
    private Integer practiceCount;

    /**
     * 学习进度
     */
    private Integer progress;

    /**
     * 分类标签
     */
    private List<String> categories;

    /**
     * 是否收藏
     */
    private Boolean isBookmarked;

    /**
     * 专业ID
     */
    private String majorId;

    /**
     * 创建时间
     */
    private String createdAt;

    /**
     * 更新时间
     */
    private String updatedAt;
}
