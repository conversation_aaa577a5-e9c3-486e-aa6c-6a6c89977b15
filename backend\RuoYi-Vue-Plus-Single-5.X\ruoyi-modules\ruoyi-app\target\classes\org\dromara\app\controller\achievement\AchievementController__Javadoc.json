{"doc": " 成就系统控制器\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "getAllBadges", "paramTypes": ["java.lang.String", "java.lang.Bo<PERSON>an", "java.lang.String"], "doc": " 获取用户所有徽章\n"}, {"name": "getBadgeDetail", "paramTypes": ["java.lang.String"], "doc": " 获取徽章详情\n"}, {"name": "pinBadge", "paramTypes": ["org.dromara.app.controller.achievement.AchievementController.PinBadgeRequest"], "doc": " 设置徽章置顶状态\n"}, {"name": "getPinnedBadges", "paramTypes": [], "doc": " 获取置顶徽章列表\n"}, {"name": "getAchievementStats", "paramTypes": [], "doc": " 获取成就统计信息\n"}, {"name": "getRecentAchievements", "paramTypes": ["java.lang.Integer"], "doc": " 获取最近解锁的成就\n"}, {"name": "getCategories", "paramTypes": [], "doc": " 获取成就分类列表\n"}, {"name": "getInProgressAchievements", "paramTypes": [], "doc": " 查询用户进行中的成就\n"}, {"name": "getAchievementDetail", "paramTypes": ["java.lang.String"], "doc": " 获取用户成就详情\n"}, {"name": "checkAchievements", "paramTypes": [], "doc": " 手动检查成就进度\n"}, {"name": "shareAchievements", "paramTypes": ["org.dromara.app.controller.achievement.AchievementController.ShareRequest"], "doc": " 分享成就墙\n"}, {"name": "getRecommendedAchievements", "paramTypes": ["java.lang.Integer"], "doc": " 获取推荐成就\n"}, {"name": "getLeaderboard", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 获取成就排行榜\n"}, {"name": "getUserRanking", "paramTypes": ["java.lang.String"], "doc": " 获取用户排名信息\n"}, {"name": "recordEvent", "paramTypes": ["org.dromara.app.controller.achievement.AchievementController.EventRequest"], "doc": " 记录用户行为事件\n"}, {"name": "getUserAchievementCompletion", "paramTypes": [], "doc": " 获取用户成就完成度\n"}, {"name": "getEventStatistics", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 获取事件统计信息\n"}, {"name": "unlockAchievement", "paramTypes": ["org.dromara.app.controller.achievement.AchievementController.UnlockRequest"], "doc": " 手动解锁成就\n"}, {"name": "recalculateUserProgress", "paramTypes": [], "doc": " 重新计算用户进度\n"}, {"name": "list", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询成就列表（分页）\n"}, {"name": "getActiveAchievements", "paramTypes": [], "doc": " 查询激活的成就列表\n"}, {"name": "getAchievementsByType", "paramTypes": ["java.lang.String"], "doc": " 根据成就类型查询成就列表\n"}, {"name": "initUserAchievements", "paramTypes": [], "doc": " 初始化用户成就进度\n"}, {"name": "checkUserAchievements", "paramTypes": [], "doc": " 手动检查用户成就\n"}], "constructors": []}