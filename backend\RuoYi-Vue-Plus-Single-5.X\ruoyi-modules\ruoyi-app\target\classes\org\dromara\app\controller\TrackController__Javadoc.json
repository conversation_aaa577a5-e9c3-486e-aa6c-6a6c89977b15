{"doc": " 埋点控制器(用于成就检测)\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "trackEvent", "paramTypes": ["org.dromara.app.domain.dto.TrackEventDto", "jakarta.servlet.http.HttpServletRequest"], "doc": " 记录用户行为埋点\n"}, {"name": "trackEvents", "paramTypes": ["java.util.List", "jakarta.servlet.http.HttpServletRequest"], "doc": " 批量记录用户行为埋点\n"}, {"name": "getClientIP", "paramTypes": ["jakarta.servlet.http.HttpServletRequest"], "doc": " 获取客户端IP地址\n"}], "constructors": []}