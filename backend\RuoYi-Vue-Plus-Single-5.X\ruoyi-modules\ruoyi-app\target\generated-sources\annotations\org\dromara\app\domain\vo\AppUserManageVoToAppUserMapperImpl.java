package org.dromara.app.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.app.domain.AppUser;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T15:35:31+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.10 (Oracle Corporation)"
)
@Component
public class AppUserManageVoToAppUserMapperImpl implements AppUserManageVoToAppUserMapper {

    @Override
    public AppUser convert(AppUserManageVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppUser appUser = new AppUser();

        appUser.setCreateTime( arg0.getCreateTime() );
        appUser.setUpdateTime( arg0.getUpdateTime() );
        appUser.setUserId( arg0.getUserId() );
        appUser.setPhone( arg0.getPhone() );
        appUser.setEmail( arg0.getEmail() );
        appUser.setRealName( arg0.getRealName() );
        appUser.setGender( arg0.getGender() );
        appUser.setStudentId( arg0.getStudentId() );
        appUser.setMajor( arg0.getMajor() );
        appUser.setGrade( arg0.getGrade() );
        appUser.setSchool( arg0.getSchool() );
        appUser.setIntroduction( arg0.getIntroduction() );
        appUser.setAvatar( arg0.getAvatar() );
        appUser.setStatus( arg0.getStatus() );
        appUser.setLoginIp( arg0.getLoginIp() );
        appUser.setLoginDate( arg0.getLoginDate() );
        appUser.setRegisteredAt( arg0.getRegisteredAt() );
        appUser.setRemark( arg0.getRemark() );

        return appUser;
    }

    @Override
    public AppUser convert(AppUserManageVo arg0, AppUser arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setPhone( arg0.getPhone() );
        arg1.setEmail( arg0.getEmail() );
        arg1.setRealName( arg0.getRealName() );
        arg1.setGender( arg0.getGender() );
        arg1.setStudentId( arg0.getStudentId() );
        arg1.setMajor( arg0.getMajor() );
        arg1.setGrade( arg0.getGrade() );
        arg1.setSchool( arg0.getSchool() );
        arg1.setIntroduction( arg0.getIntroduction() );
        arg1.setAvatar( arg0.getAvatar() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setLoginIp( arg0.getLoginIp() );
        arg1.setLoginDate( arg0.getLoginDate() );
        arg1.setRegisteredAt( arg0.getRegisteredAt() );
        arg1.setRemark( arg0.getRemark() );

        return arg1;
    }
}
