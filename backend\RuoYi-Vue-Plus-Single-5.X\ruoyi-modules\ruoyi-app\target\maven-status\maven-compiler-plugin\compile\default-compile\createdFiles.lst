org\dromara\app\tool\ToolExecutor.class
org\dromara\app\domain\vo\RecommendationResponseVo$RecommendationItemVo.class
org\dromara\app\service\IFileService$SpeechToTextResult__Javadoc.json
org\dromara\app\controller\interview\InterviewQuestionController$DifficultyDistribution__Javadoc.json
org\dromara\app\mapper\VideoProgressMapper__Javadoc.json
org\dromara\app\config\PaymentTimeoutConfig__Javadoc.json
org\dromara\app\domain\Video__Javadoc.json
org\dromara\app\controller\agent\ToolController__Javadoc.json
org\dromara\app\domain\vo\VideoCommentVo__Javadoc.json
org\dromara\app\domain\vo\InterviewResultResponseVo$ImprovementPlan.class
org\dromara\app\domain\LearningProgress__Javadoc.json
org\dromara\app\utils\QuestionUtils__Javadoc.json
org\dromara\app\controller\XunfeiTestController__Javadoc.json
org\dromara\app\domain\ResultShare__Javadoc.json
org\dromara\app\domain\dto\avatar\AvatarTextDto__Javadoc.json
org\dromara\app\service\IQuestionManagementService$QuestionCoverageAnalysis__Javadoc.json
org\dromara\app\domain\ChatMessage$MessageAttachment__Javadoc.json
org\dromara\app\service\IInterviewQuestionService.class
org\dromara\app\service\ChatMemoryManager$MemoryStats__Javadoc.json
org\dromara\app\service\IQuestionCommentService__Javadoc.json
org\dromara\app\service\ILearningResourceService.class
org\dromara\app\test_dev\AuthUtil__Javadoc.json
org\dromara\app\service\ILearningRecommendationService__Javadoc.json
org\dromara\app\event\SimpleAchievementEventListener__Javadoc.json
org\dromara\app\domain\bo\AchievementBo__Javadoc.json
org\dromara\app\domain\dto\ActivityHistoryRequest__Javadoc.json
org\dromara\app\mapper\VideoPlayRecordMapper__Javadoc.json
org\dromara\app\service\IFeedbackService__Javadoc.json
org\dromara\app\domain\dto\ChatRequestDto__Javadoc.json
org\dromara\app\mapper\AssessmentQuestionMapper.class
org\dromara\app\service\impl\PromptEngineeringServiceImpl__Javadoc.json
org\dromara\app\domain\bo\VideoQueryBo__Javadoc.json
org\dromara\app\domain\vo\BadgeVo.class
org\dromara\app\service\PromptEngineeringService$ConversationTurn.class
org\dromara\app\service\impl\InterviewServiceImpl__Javadoc.json
org\dromara\app\event\InterviewEvents$InterviewCompletedEvent__Javadoc.json
org\dromara\app\service\impl\UserResumeServiceImpl.class
org\dromara\app\mapper\InterviewHistoryMapper__Javadoc.json
org\dromara\app\domain\vo\SmartTaskVO.class
org\dromara\app\event\SimpleAchievementEventListener$VideoWatchEvent__Javadoc.json
org\dromara\app\config\InterviewWebSocketConfig__Javadoc.json
org\dromara\app\controller\TrackController__Javadoc.json
org\dromara\app\controller\interview\InterviewSessionController__Javadoc.json
org\dromara\app\mapper\QuestionAnalysisMapper__Javadoc.json
org\dromara\app\service\IOllamaService$ModelInfo.class
org\dromara\app\domain\vo\InterviewResponseVo$InterviewResult__Javadoc.json
org\dromara\app\service\IMultimodalAnalysisService$TextAnalysisResult__Javadoc.json
org\dromara\app\domain\SessionQuestion__Javadoc.json
org\dromara\app\mapper\KnowledgeDocumentMapper__Javadoc.json
org\dromara\app\domain\bo\InterviewResultBo$SaveToHistoryRequest__Javadoc.json
org\dromara\app\controller\interview\InterviewController$EndSessionRequest__Javadoc.json
org\dromara\app\domain\QuestionBank__Javadoc.json
org\dromara\app\exception\InterviewException$InterviewModeNotFoundException__Javadoc.json
org\dromara\app\domain\DimensionScore__Javadoc.json
org\dromara\app\domain\dto\ActivitySyncRequest__Javadoc.json
org\dromara\app\service\impl\AppUserProfileServiceImpl.class
org\dromara\app\domain\vo\AccessControlStatusVo__Javadoc.json
org\dromara\app\service\IInterviewCacheService__Javadoc.json
org\dromara\app\service\IInterviewQuestionService$QuestionsByDifficulty__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$AnswerResponse__Javadoc.json
org\dromara\app\mapper\AiToolMapper.class
org\dromara\app\service\IUserResumeService.class
org\dromara\app\domain\vo\RecommendationResponseVo$RecommendationItemVo__Javadoc.json
org\dromara\app\controller\learning\LearningRecommendationController__Javadoc.json
org\dromara\app\controller\chat\SparkController__Javadoc.json
org\dromara\app\service\impl\VideoServiceImpl__Javadoc.json
org\dromara\app\service\IUserResumeService__Javadoc.json
org\dromara\app\service\impl\RagServiceImpl__Javadoc.json
org\dromara\app\domain\dto\InterviewReportData__Javadoc.json
org\dromara\app\domain\enums\ActivityType__Javadoc.json
org\dromara\app\service\tool\ToolExecutor$ExecutionContext__Javadoc.json
org\dromara\app\test_dev\AvatarWsUtil$1.class
org\dromara\app\domain\vo\RelatedVideoVo__Javadoc.json
org\dromara\app\service\AdvancedRagService$QueryExpansionType__Javadoc.json
org\dromara\app\domain\vo\ActivitySessionVO.class
org\dromara\app\event\InterviewEvents$SystemErrorEvent__Javadoc.json
org\dromara\app\domain\vo\PracticeVo.class
org\dromara\app\domain\AiTool$ParameterSchema$ParameterProperty.class
org\dromara\app\domain\bo\InterviewResultBo$ShareResultRequest__Javadoc.json
org\dromara\app\domain\vo\VideoDetailVo__Javadoc.json
org\dromara\app\config\InterviewConfig$Sort__Javadoc.json
org\dromara\app\config\XunfeiConfig$SpeechConfig__Javadoc.json
org\dromara\app\service\IToolService__Javadoc.json
org\dromara\app\controller\system\QuestionController__Javadoc.json
org\dromara\app\domain\LearningProgress$PlanAdjustment.class
org\dromara\app\mapper\AssessmentQuestionOptionMapper__Javadoc.json
org\dromara\app\config\AppStartupConfiguration__Javadoc.json
org\dromara\app\service\impl\PaymentServiceImpl__Javadoc.json
org\dromara\app\controller\interview\InterviewController__Javadoc.json
org\dromara\app\mapper\VideoMetricsMapper__Javadoc.json
org\dromara\app\domain\vo\ActivityHistoryVO.class
org\dromara\app\service\IAchievementNotificationService$NotificationVo__Javadoc.json
org\dromara\app\domain\vo\JobVo__Javadoc.json
org\dromara\app\domain\vo\UserResumeVo.class
org\dromara\app\service\impl\AchievementTriggerService$InterviewCompletedEvent__Javadoc.json
org\dromara\app\service\IMultimodalAnalysisService$AudioAnalysisResult__Javadoc.json
org\dromara\app\service\IToolCallService$ParameterValidationResult.class
org\dromara\app\config\OllamaConfig$OllamaProperties__Javadoc.json
org\dromara\app\mapper\ChatMessageMapper__Javadoc.json
org\dromara\app\service\IReportGenerationService$BasicInfo__Javadoc.json
org\dromara\app\service\IMultimodalAnalysisService$MultimodalAnalysisResult__Javadoc.json
org\dromara\app\domain\dto\AppUserProfileDto__Javadoc.json
org\dromara\app\service\impl\PaymentSseServiceImpl__Javadoc.json
org\dromara\app\service\IToolService$ParameterValidationResult__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$CategoryInfo__Javadoc.json
org\dromara\app\service\SseConnectionManager__Javadoc.json
org\dromara\app\mapper\InterviewReportMapper.class
org\dromara\app\service\IQuestionManagementService__Javadoc.json
org\dromara\app\domain\ToolCall$CallContext.class
org\dromara\app\service\IMultimodalAnalysisService$VideoAnalysisResult__Javadoc.json
org\dromara\app\service\SseConnectionManager$ConnectionWrapper__Javadoc.json
org\dromara\app\domain\ToolCall$CallContext__Javadoc.json
org\dromara\app\event\InterviewEvents$QuestionSkippedEvent__Javadoc.json
org\dromara\app\controller\achievement\AchievementManageController__Javadoc.json
org\dromara\app\engine\AchievementRuleEngine$AchievementProgress__Javadoc.json
org\dromara\app\domain\vo\InterviewResultResponseVo$PerformanceMetrics__Javadoc.json
org\dromara\app\service\IChartGenerationService__Javadoc.json
org\dromara\app\service\IAppUserProfileService.class
org\dromara\app\service\impl\PaymentSseServiceImpl$SseConnectionInfo__Javadoc.json
org\dromara\app\service\ConfigurationService__Javadoc.json
org\dromara\app\controller\agent\AgentController__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$UserStats__Javadoc.json
org\dromara\app\service\IQuestionManagementService$DifficultyLevel__Javadoc.json
org\dromara\app\domain\VideoPlayRecord__Javadoc.json
org\dromara\app\service\IFileService__Javadoc.json
org\dromara\app\domain\QuestionComment__Javadoc.json
org\dromara\app\service\impl\XunfeiAvatarServiceImpl__Javadoc.json
org\dromara\app\domain\DocumentChunk__Javadoc.json
org\dromara\app\domain\VideoCommentLike__Javadoc.json
org\dromara\app\domain\InterviewReport$LearningResource__Javadoc.json
org\dromara\app\domain\dto\QuestionBankQueryDto__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$InterviewQuestion__Javadoc.json
org\dromara\app\service\IReportGenerationService$DimensionScore__Javadoc.json
org\dromara\app\service\IPaymentService__Javadoc.json
org\dromara\app\domain\ActivitySession__Javadoc.json
org\dromara\app\controller\common\FeedbackController__Javadoc.json
org\dromara\app\service\ILearningRecommendationService$LearningResource__Javadoc.json
org\dromara\app\event\PaymentEvent$PaymentSuccessEvent__Javadoc.json
org\dromara\app\test_dev\Main$1.class
org\dromara\app\controller\learning\LearningRecommendationController$WeaknessRecommendationRequest__Javadoc.json
org\dromara\app\service\impl\LearningRecommendationServiceImpl__Javadoc.json
org\dromara\app\controller\achievement\AchievementManageController$PreviewRequest__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$FeedbackInfo__Javadoc.json
org\dromara\app\controller\WebSocketTestController__Javadoc.json
org\dromara\app\service\impl\AchievementServiceImpl__Javadoc.json
org\dromara\app\domain\VideoMetrics__Javadoc.json
org\dromara\app\service\IEmbeddingService__Javadoc.json
org\dromara\app\controller\learning\BookController__Javadoc.json
org\dromara\app\domain\Question__Javadoc.json
org\dromara\app\domain\vo\KnowledgeDocumentVo__Javadoc.json
org\dromara\app\mapper\FeedbackMapper__Javadoc.json
org\dromara\app\domain\bo\VideoCommentBo__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$SessionStatus__Javadoc.json
org\dromara\app\domain\UserAssessmentResult.class
org\dromara\app\service\impl\UserBehaviorServiceImpl__Javadoc.json
org\dromara\app\mapper\QuestionCommentMapper__Javadoc.json
org\dromara\app\service\IToolCallService.class
org\dromara\app\mapper\InterviewReportMapper__Javadoc.json
org\dromara\app\domain\vo\VideoListResultVo__Javadoc.json
org\dromara\app\domain\ChatMessage__Javadoc.json
org\dromara\app\service\IReportGenerationService__Javadoc.json
org\dromara\app\mapper\VideoCollectMapper__Javadoc.json
org\dromara\app\domain\ActivityStatistics__Javadoc.json
org\dromara\app\controller\interview\InterviewResultController__Javadoc.json
org\dromara\app\controller\interview\InterviewSessionController$SubmitAnswerResponse__Javadoc.json
org\dromara\app\domain\vo\InitialAbilityAssessmentVo__Javadoc.json
org\dromara\app\controller\achievement\AchievementManageController$TestRuleRequest__Javadoc.json
org\dromara\app\mapper\JobCategoryMapper.class
org\dromara\app\domain\InterviewQuestion__Javadoc.json
org\dromara\app\domain\vo\AppUserProfileVo__Javadoc.json
org\dromara\app\service\impl\QuestionManagementServiceImpl__Javadoc.json
org\dromara\app\exception\InterviewResultExceptionHandler$InsufficientPermissionException__Javadoc.json
org\dromara\app\domain\DimensionScore.class
org\dromara\app\domain\LearningProgress$PhaseGoal__Javadoc.json
org\dromara\app\service\IMultimodalAnalysisService$DimensionScore__Javadoc.json
org\dromara\app\service\IJobService.class
org\dromara\app\service\IXunfeiService$EmotionAnalysisResult__Javadoc.json
org\dromara\app\mapper\ImprovementPlanMapper__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$SessionInfo__Javadoc.json
org\dromara\app\service\impl\AchievementTriggerService__Javadoc.json
org\dromara\app\domain\dto\ActivityStartRequest__Javadoc.json
org\dromara\app\domain\vo\FeedbackVo__Javadoc.json
org\dromara\app\service\PromptEngineeringService$ConversationTurn__Javadoc.json
org\dromara\app\service\tool\ToolExecutor.class
org\dromara\app\domain\bo\UserBehaviorBo__Javadoc.json
org\dromara\app\service\IXunfeiService$VoiceConfig__Javadoc.json
org\dromara\app\mapper\VideoCommentMapper__Javadoc.json
org\dromara\app\service\AdvancedRagService$SmartRetrievalResult__Javadoc.json
org\dromara\app\domain\ChatMessage$MessageMetadata__Javadoc.json
org\dromara\app\domain\vo\SlowQueryVo__Javadoc.json
org\dromara\app\domain\vo\InterviewResultResponseVo$SaveHistoryResponse__Javadoc.json
org\dromara\app\domain\vo\DashboardSummaryVO.class
org\dromara\app\domain\dto\ActivityStatisticsRequest__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$QuestionListResponse__Javadoc.json
org\dromara\app\service\IBookReadingRecordService__Javadoc.json
org\dromara\app\domain\vo\DashboardSummaryVO$UserInfoVO__Javadoc.json
org\dromara\app\mapper\SessionQuestionMapper__Javadoc.json
org\dromara\app\config\OllamaConfig__Javadoc.json
org\dromara\app\controller\interview\InterviewQuestionController$QuestionTypeStats__Javadoc.json
org\dromara\app\domain\vo\UserGrowthProfileVo__Javadoc.json
org\dromara\app\domain\vo\ActivityHistoryVO__Javadoc.json
org\dromara\app\service\avatar\XunfeiAvatarProtocolService__Javadoc.json
org\dromara\app\domain\bo\QuestionBo__Javadoc.json
org\dromara\app\service\impl\XunfeiServiceImpl$AgentContext__Javadoc.json
org\dromara\app\domain\vo\InterviewResultResponseVo$ImprovementArea__Javadoc.json
org\dromara\app\service\impl\EmbeddingServiceImpl__Javadoc.json
org\dromara\app\domain\UserBadge__Javadoc.json
org\dromara\app\service\impl\PaymentSseServiceImpl$PaymentFailedData__Javadoc.json
org\dromara\app\domain\vo\InterviewResultResponseVo$ImprovementPlan__Javadoc.json
org\dromara\app\service\impl\AchievementTriggerService$ConsecutiveLoginEvent__Javadoc.json
org\dromara\app\event\InterviewEvents$AiEvaluationEvent__Javadoc.json
org\dromara\app\exception\BaseBusinessException__Javadoc.json
org\dromara\app\controller\interview\InterviewController$SubmitAnswerRequest__Javadoc.json
org\dromara\app\domain\AudioMetrics__Javadoc.json
org\dromara\app\service\IActivityTimerService__Javadoc.json
org\dromara\app\service\PromptEngineeringService$PromptExample.class
org\dromara\app\domain\vo\InterviewResponseVo$JobStats__Javadoc.json
org\dromara\app\mapper\InstructorFollowMapper__Javadoc.json
org\dromara\app\domain\bo\CommentQueryBo__Javadoc.json
org\dromara\app\service\IAchievementNotificationService__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$InterviewStep__Javadoc.json
org\dromara\app\exception\InterviewException$SessionExpiredException__Javadoc.json
org\dromara\app\event\PaymentEvent$PaymentCancelledEvent__Javadoc.json
org\dromara\app\service\IAchievementManageService__Javadoc.json
org\dromara\app\mapper\InterviewAnswerMapper.class
org\dromara\app\service\IQuestionBankService__Javadoc.json
org\dromara\app\exception\ChatException__Javadoc.json
org\dromara\app\domain\AssessmentQuestion.class
org\dromara\app\service\AdvancedRagService$QueryExpansionType.class
org\dromara\app\domain\AchievementEvent__Javadoc.json
org\dromara\app\domain\vo\TaskQueueStatusVo__Javadoc.json
org\dromara\app\service\IOllamaService$ChatMessage__Javadoc.json
org\dromara\app\domain\vo\InterviewResultResponseVo.class
org\dromara\app\domain\ChatSession$SessionStats__Javadoc.json
org\dromara\app\domain\vo\InterviewResultResponseVo$AudioMetrics__Javadoc.json
org\dromara\app\domain\vo\AchievementStatsVo$RarityStats__Javadoc.json
org\dromara\app\domain\VideoLike__Javadoc.json
org\dromara\app\service\impl\JobServiceImpl__Javadoc.json
org\dromara\app\service\impl\AppUserServiceImpl__Javadoc.json
org\dromara\app\domain\AppUser.class
org\dromara\app\domain\JobFavorite__Javadoc.json
org\dromara\app\domain\LearningProgress$LearningStatistics__Javadoc.json
org\dromara\app\domain\vo\SensitiveInfoDetectionVo$SensitiveWordInfo__Javadoc.json
org\dromara\app\domain\vo\InterviewResultVo__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$InterviewRecord__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$SystemStats__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$DimensionScores__Javadoc.json
org\dromara\app\domain\vo\VideoCommentListVo.class
org\dromara\app\domain\vo\PermissionCheckResultVo__Javadoc.json
org\dromara\app\event\PaymentEvent__Javadoc.json
org\dromara\app\service\impl\ChatServiceImpl__Javadoc.json
org\dromara\app\service\impl\VideoPlayRecordCacheService__Javadoc.json
org\dromara\app\domain\dto\TrackEventDto__Javadoc.json
org\dromara\app\exception\InterviewResultExceptionHandler$ShareLinkExpiredException__Javadoc.json
org\dromara\app\controller\learning\LearningRecommendController__Javadoc.json
org\dromara\app\domain\AiTool$ToolConfig.class
org\dromara\app\domain\JobCategory__Javadoc.json
org\dromara\app\event\PaymentEvent$PaymentFailedEvent__Javadoc.json
org\dromara\app\controller\pay\PayController__Javadoc.json
org\dromara\app\mapper\ToolCallMapper__Javadoc.json
org\dromara\app\websocket\InterviewWebSocketHandler__Javadoc.json
org\dromara\app\domain\vo\JobVo.class
org\dromara\app\domain\bo\InterviewResultBo$UserResultsRequest__Javadoc.json
org\dromara\app\mapper\UserResumeMapper.class
org\dromara\app\domain\bo\QuestionBankBo.class
org\dromara\app\domain\AssessmentQuestionOption__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$FeedbackResponse__Javadoc.json
org\dromara\app\domain\vo\JobCategoryVo__Javadoc.json
org\dromara\app\controller\agent\KnowledgeController$SearchRequest__Javadoc.json
org\dromara\app\domain\Feedback__Javadoc.json
org\dromara\app\service\IInterviewService__Javadoc.json
org\dromara\app\controller\learning\LearningProgressController.class
org\dromara\app\domain\ToolCall$ToolCallResult__Javadoc.json
org\dromara\app\domain\LearningProgress$LearningStatistics.class
org\dromara\app\domain\bo\QuestionBankBo__Javadoc.json
org\dromara\app\controller\interview\InterviewController$ShareJobRequest__Javadoc.json
org\dromara\app\service\IQuestionTagService$TagsByCategory__Javadoc.json
org\dromara\app\domain\vo\InterviewResultResponseVo$ShareResultResponse.class
org\dromara\app\domain\vo\UserAchievementVo__Javadoc.json
org\dromara\app\domain\dto\ChatResponseDto__Javadoc.json
org\dromara\app\service\IQuestionTagService__Javadoc.json
org\dromara\app\domain\vo\KnowledgeBaseVo__Javadoc.json
org\dromara\app\service\IReportGenerationService$ImprovementSuggestion__Javadoc.json
org\dromara\app\mapper\JobMapper$JobStatistics.class
org\dromara\app\service\impl\OllamaServiceImpl__Javadoc.json
org\dromara\app\domain\LearningResource.class
org\dromara\app\domain\vo\DashboardSummaryVO$UserInfoVO.class
org\dromara\app\controller\chat\SparkController$ChatRequest__Javadoc.json
org\dromara\app\mapper\VideoLikeMapper__Javadoc.json
org\dromara\app\domain\InterviewAnswer.class
org\dromara\app\config\XunfeiAvatarConfig__Javadoc.json
org\dromara\app\domain\dto\AppAuthDto.class
org\dromara\app\tool\ToolExecutor__Javadoc.json
org\dromara\app\domain\dto\AppUserProfileDto.class
org\dromara\app\mapper\InterviewModeMapper__Javadoc.json
org\dromara\app\domain\vo\EncryptionStatusVo__Javadoc.json
org\dromara\app\domain\Badge__Javadoc.json
org\dromara\app\service\IRagService__Javadoc.json
org\dromara\app\domain\InterviewReport$RadarChartData.class
org\dromara\app\domain\LearningProgress$LearningFeedback.class
org\dromara\app\domain\PaymentOrder__Javadoc.json
org\dromara\app\mapper\ChatSessionMapper__Javadoc.json
org\dromara\app\mapper\AudioMetricsMapper__Javadoc.json
org\dromara\app\domain\dto\InterviewReportData$RadarChartData__Javadoc.json
org\dromara\app\controller\TimerController__Javadoc.json
org\dromara\app\test_dev\AvatarWsUtil.class
org\dromara\app\domain\BookReadingRecord__Javadoc.json
org\dromara\app\service\impl\AchievementTriggerService$AbilityImproveEvent__Javadoc.json
org\dromara\app\domain\Agent__Javadoc.json
org\dromara\app\service\ILearningService__Javadoc.json
org\dromara\app\service\IUserBehaviorService__Javadoc.json
org\dromara\app\utils\InterviewUtils__Javadoc.json
org\dromara\app\service\tool\ToolExecutor$ExecutionContext.class
org\dromara\app\mapper\LearningProgressMapper__Javadoc.json
org\dromara\app\exception\InterviewException__Javadoc.json
org\dromara\app\controller\interview\JobController__Javadoc.json
org\dromara\app\domain\AiTool__Javadoc.json
org\dromara\app\service\impl\FileServiceImpl$FileInfo__Javadoc.json
org\dromara\app\mapper\UserBehaviorMapper__Javadoc.json
org\dromara\app\domain\vo\AppUserProfileVo.class
org\dromara\app\service\impl\AchievementManageServiceImpl__Javadoc.json
org\dromara\app\service\impl\XunfeiServiceImpl__Javadoc.json
org\dromara\app\service\avatar\XunfeiAvatarAuthService__Javadoc.json
org\dromara\app\domain\ImprovementPlan__Javadoc.json
org\dromara\app\domain\UserAssessmentRecord__Javadoc.json
org\dromara\app\domain\dto\InterviewReportData$BasicInfo__Javadoc.json
org\dromara\app\mapper\BookMapper__Javadoc.json
org\dromara\app\domain\dto\InterviewReportData$LearningPathRecommendation__Javadoc.json
org\dromara\app\domain\dto\PaymentOrderDto__Javadoc.json
org\dromara\app\config\InterviewConfig$Session__Javadoc.json
org\dromara\app\service\IXunfeiService$StreamCallback__Javadoc.json
org\dromara\app\service\impl\InterviewAnalysisServiceImpl__Javadoc.json
org\dromara\app\domain\LearningProgress$PhaseGoal.class
org\dromara\app\domain\vo\StudyStatsVO__Javadoc.json
org\dromara\app\domain\vo\UserResumeUploadVo__Javadoc.json
org\dromara\app\domain\LearningResource$UsageStatistics.class
org\dromara\app\domain\vo\InterviewResponseVo$JobListResponse__Javadoc.json
org\dromara\app\service\IOllamaService.class
org\dromara\app\event\AchievementEventListener$LearningCompletedEvent__Javadoc.json
org\dromara\app\domain\InterviewReport$LearningPathRecommendation.class
org\dromara\app\service\IAgentService__Javadoc.json
org\dromara\app\service\IQuestionManagementService$MultimodalEvaluationConfig__Javadoc.json
org\dromara\app\domain\dto\ChatRequestDto$MessageAttachment__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$PopularQuestion__Javadoc.json
org\dromara\app\domain\vo\OptionVo__Javadoc.json
org\dromara\app\service\impl\XunfeiServiceImpl$StreamingChatResponseHandler__Javadoc.json
org\dromara\app\config\InterviewConfig__Javadoc.json
org\dromara\app\domain\InterviewReport$LearningResource.class
org\dromara\app\domain\DocumentChunk$ChunkMetadata__Javadoc.json
org\dromara\app\domain\vo\AppUserInfoVo.class
org\dromara\app\mapper\UserResumeMapper__Javadoc.json
org\dromara\app\domain\vo\InterviewResultResponseVo$InterviewResultSummary__Javadoc.json
org\dromara\app\controller\system\QuestionBankController__Javadoc.json
org\dromara\app\exception\InterviewException$JobNotFoundException__Javadoc.json
org\dromara\app\mapper\UserBadgeMapper__Javadoc.json
org\dromara\app\controller\chat\SparkController$AgentChatRequest__Javadoc.json
org\dromara\app\domain\vo\RecommendationsVo__Javadoc.json
org\dromara\app\domain\dto\avatar\AvatarStartDto__Javadoc.json
org\dromara\app\config\XunfeiConfig$SparkConfig__Javadoc.json
org\dromara\app\event\AchievementEventPublisher__Javadoc.json
org\dromara\app\service\impl\LearningRecommendServiceImpl.class
org\dromara\app\service\ITaskQueueService.class
org\dromara\app\service\impl\AgentServiceImpl__Javadoc.json
org\dromara\app\service\IQuestionService__Javadoc.json
org\dromara\app\config\PaymentRabbitMqConfig__Javadoc.json
org\dromara\app\service\impl\InterviewStatsServiceImpl__Javadoc.json
org\dromara\app\domain\vo\InterviewResultResponseVo$QuestionAnalysis__Javadoc.json
org\dromara\app\service\tool\impl\CurrentTimeToolExecutor.class
org\dromara\app\controller\agent\ToolController$BatchToolExecuteRequest__Javadoc.json
org\dromara\app\domain\bo\UserResumeBo__Javadoc.json
org\dromara\app\domain\UserBehavior__Javadoc.json
org\dromara\app\service\ICacheOptimizationService__Javadoc.json
org\dromara\app\domain\UserAssessmentResult__Javadoc.json
org\dromara\app\mapper\BadgeMapper__Javadoc.json
org\dromara\app\service\impl\ToolServiceImpl__Javadoc.json
org\dromara\app\mapper\ResultShareMapper__Javadoc.json
org\dromara\app\exception\InterviewResultExceptionHandler$InterviewResultAlreadyExistsException__Javadoc.json
org\dromara\app\domain\dto\InterviewReportData$DimensionScore__Javadoc.json
org\dromara\app\config\XunfeiConfig__Javadoc.json
org\dromara\app\domain\bo\KnowledgeDocumentBo__Javadoc.json
org\dromara\app\domain\dto\QuestionQueryDto__Javadoc.json
org\dromara\app\service\impl\QuestionServiceImpl__Javadoc.json
org\dromara\app\service\impl\ToolCallServiceImpl.class
org\dromara\app\controller\achievement\AchievementEventTestController.class
org\dromara\app\domain\vo\InterviewResponseVo$JobInterviewModesResponse__Javadoc.json
org\dromara\app\domain\VideoShare__Javadoc.json
org\dromara\app\domain\ToolCall$ToolCallResult.class
org\dromara\app\mapper\BookMapper.class
org\dromara\app\controller\AssessmentController__Javadoc.json
org\dromara\app\domain\AudioMetrics.class
org\dromara\app\exception\InterviewException$UserNotLoginException__Javadoc.json
org\dromara\app\controller\interview\InterviewController$FavoriteJobRequest__Javadoc.json
org\dromara\app\domain\JobFavorite.class
org\dromara\app\domain\vo\InterviewResultResponseVo$DimensionScore__Javadoc.json
org\dromara\app\service\impl\FeedbackServiceImpl__Javadoc.json
org\dromara\app\service\IQuestionManagementService$DifficultyGradingSystem.class
org\dromara\app\domain\vo\DatabasePerformanceVo__Javadoc.json
org\dromara\app\domain\vo\InterviewResultResponseVo$InterviewResultDetail.class
org\dromara\app\domain\Book.class
org\dromara\app\mapper\InterviewResultMapper.class
org\dromara\app\mapper\JobFavoriteMapper.class
org\dromara\app\service\impl\LearningResourceServiceImpl__Javadoc.json
org\dromara\app\domain\vo\VideoLearningStatsVo__Javadoc.json
org\dromara\app\mapper\AudioMetricsMapper.class
org\dromara\app\event\SimpleAchievementEventListener$UserLoginEvent__Javadoc.json
org\dromara\app\domain\vo\InterviewResultResponseVo$AudioMetrics.class
org\dromara\app\service\IInterviewQuestionService__Javadoc.json
org\dromara\app\domain\InterviewMode.class
org\dromara\app\domain\LearningProgress$LearningFeedback__Javadoc.json
org\dromara\app\service\impl\QuestionManagementServiceImpl.class
org\dromara\app\config\OllamaConfig.class
org\dromara\app\mapper\SearchKeywordMapper__Javadoc.json
org\dromara\app\domain\Agent$ModelConfig__Javadoc.json
org\dromara\app\service\impl\PaymentSseServiceImpl$SseMessage__Javadoc.json
org\dromara\app\controller\interview\InterviewController$CreateSessionRequest__Javadoc.json
org\dromara\app\service\IXunfeiService$VoiceEmotionResult__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo__Javadoc.json
org\dromara\app\mapper\ChatMessageMapper.class
org\dromara\app\domain\dto\avatar\AvatarStartDto.class
org\dromara\app\mapper\UserGrowthProfileMapper__Javadoc.json
org\dromara\app\service\AdvancedRagService__Javadoc.json
org\dromara\app\service\IAiEvaluationService__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$AnswerInfo__Javadoc.json
org\dromara\app\domain\bo\JobQueryBo__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$UserReadinessResponse__Javadoc.json
org\dromara\app\controller\interview\InterviewController$EndInterviewRequest__Javadoc.json
org\dromara\app\config\PaymentSseConfig.class
org\dromara\app\domain\UserGrowthProfile__Javadoc.json
org\dromara\app\mapper\LearningResourceMapper__Javadoc.json
org\dromara\app\event\AchievementEventListener__Javadoc.json
org\dromara\app\service\ILearningRecommendationService$LearningPathRecommendation__Javadoc.json
org\dromara\app\mapper\AppUserMapper.class
org\dromara\app\domain\ChatMessage$MessageAttachment.class
org\dromara\app\controller\achievement\AchievementController$PinBadgeRequest__Javadoc.json
org\dromara\app\service\impl\AchievementTriggerService$UserLoginEvent__Javadoc.json
org\dromara\app\domain\VectorEmbedding__Javadoc.json
org\dromara\app\event\AchievementEventListener$UserRegistrationEvent__Javadoc.json
org\dromara\app\domain\dto\avatar\AvatarTextDto.class
org\dromara\app\tool\impl\TextProcessorToolExecutor__Javadoc.json
org\dromara\app\service\IDatabaseOptimizationService__Javadoc.json
org\dromara\app\service\avatar\XunfeiAvatarWebSocketService__Javadoc.json
org\dromara\app\service\impl\BookChapterServiceImpl__Javadoc.json
org\dromara\app\mapper\QuestionBankMapper.class
org\dromara\app\domain\vo\InterviewResponseVo$ShareJobResponse__Javadoc.json
org\dromara\app\domain\dto\ChatResponseDto$MessageMetadata__Javadoc.json
org\dromara\app\mapper\VideoCommentLikeMapper__Javadoc.json
org\dromara\app\domain\AiTool.class
org\dromara\app\domain\LearningResource__Javadoc.json
org\dromara\app\service\impl\QuestionTagServiceImpl__Javadoc.json
org\dromara\app\domain\ToolCall__Javadoc.json
org\dromara\app\service\PromptEngineeringService__Javadoc.json
org\dromara\app\service\impl\QuestionBankServiceImpl.class
org\dromara\app\service\IOllamaService$ModelInfo__Javadoc.json
org\dromara\app\config\XunfeiConfig$TtsConfig__Javadoc.json
org\dromara\app\domain\InterviewHistory__Javadoc.json
org\dromara\app\service\IInterviewStatsService__Javadoc.json
org\dromara\app\service\impl\LangChain4jChatServiceImpl__Javadoc.json
org\dromara\app\task\InterviewSessionCleanupTask__Javadoc.json
org\dromara\app\controller\auth\AppAuthController__Javadoc.json
org\dromara\app\domain\bo\InterviewResultBo$LearningResourcesRequest__Javadoc.json
org\dromara\app\service\ILearningProgressService.class
org\dromara\app\domain\vo\InterviewResponseVo$SearchSuggestionsResponse__Javadoc.json
org\dromara\app\domain\ToolCall$ToolCallResult$ToolCallResultBuilder.class
org\dromara\app\handler\InterviewExceptionHandler__Javadoc.json
org\dromara\app\service\tool\ToolExecutor$ValidationResult.class
org\dromara\app\domain\InterviewSession__Javadoc.json
org\dromara\app\domain\bo\QuestionCommentBo__Javadoc.json
org\dromara\app\domain\dto\AnalysisTaskDto__Javadoc.json
org\dromara\app\domain\InterviewReport__Javadoc.json
org\dromara\app\mapper\InterviewQuestionMapper__Javadoc.json
org\dromara\app\domain\vo\VideoCommentListVo__Javadoc.json
org\dromara\app\domain\enums\ErrorCode.class
org\dromara\app\domain\VectorEmbedding.class
org\dromara\app\domain\vo\VideoLearningStatsVo.class
org\dromara\app\domain\bo\InterviewResultBo__Javadoc.json
org\dromara\app\service\impl\QuestionBankServiceImpl__Javadoc.json
org\dromara\app\domain\vo\VideoDetailVo.class
org\dromara\app\controller\interview\InterviewQuestionController__Javadoc.json
org\dromara\app\domain\bo\UserAchievementBo__Javadoc.json
org\dromara\app\domain\ChatSession$SessionStats.class
org\dromara\app\domain\dto\ChatResponseDto.class
org\dromara\app\service\IRagService$KnowledgeBaseStats__Javadoc.json
org\dromara\app\event\PaymentEvent$PaymentTimeoutEvent__Javadoc.json
org\dromara\app\exception\InterviewResultExceptionHandler__Javadoc.json
org\dromara\app\service\IXunfeiService__Javadoc.json
org\dromara\app\service\impl\AiEvaluationServiceImpl__Javadoc.json
org\dromara\app\task\PaymentTokenCleanupTask__Javadoc.json
org\dromara\app\controller\learning\LearningResourceController__Javadoc.json
org\dromara\app\domain\InterviewReport$RadarChartData__Javadoc.json
org\dromara\app\event\AchievementEventListener$InterviewCompletedEvent__Javadoc.json
org\dromara\app\mapper\AiToolMapper__Javadoc.json
org\dromara\app\domain\InterviewReport$LearningPathRecommendation__Javadoc.json
org\dromara\app\domain\vo\InterviewResultResponseVo__Javadoc.json
org\dromara\app\domain\AiTool$FunctionDefinition__Javadoc.json
org\dromara\app\domain\JobCategory.class
org\dromara\app\domain\AiTool$FunctionDefinition.class
org\dromara\app\service\impl\AchievementTriggerService$ResumeSubmitEvent__Javadoc.json
org\dromara\app\event\InterviewEvents__Javadoc.json
org\dromara\app\domain\LearningResource$QualityAssessment__Javadoc.json
org\dromara\app\mapper\DimensionScoreMapper__Javadoc.json
org\dromara\app\domain\bo\AppUserManageBo__Javadoc.json
org\dromara\app\domain\vo\DashboardSummaryVO__Javadoc.json
org\dromara\app\controller\common\FeedbackController.class
org\dromara\app\domain\bo\AssessmentResultBo__Javadoc.json
org\dromara\app\domain\vo\ActivitySessionVO__Javadoc.json
org\dromara\app\service\PromptEngineeringService$PromptExample__Javadoc.json
org\dromara\app\domain\InterviewReport$ImprovementSuggestion__Javadoc.json
org\dromara\app\service\IAppUserProfileService__Javadoc.json
org\dromara\app\service\impl\LearningProgressServiceImpl__Javadoc.json
org\dromara\app\domain\vo\AssessmentQuestionVo__Javadoc.json
org\dromara\app\tool\impl\CalculatorToolExecutor.class
org\dromara\app\service\ILearningProgressService__Javadoc.json
org\dromara\app\controller\achievement\AchievementManageController$BatchUpdateStatusRequest__Javadoc.json
org\dromara\app\event\InterviewEventListener__Javadoc.json
org\dromara\app\domain\vo\UserResumeVo__Javadoc.json
org\dromara\app\service\IFeedbackService.class
org\dromara\app\domain\vo\InterviewResponseVo$RecommendationItem__Javadoc.json
org\dromara\app\domain\vo\QuestionDetailVO__Javadoc.json
org\dromara\app\config\AsyncConfig.class
org\dromara\app\domain\vo\InterviewResultResponseVo$ShareResultResponse__Javadoc.json
org\dromara\app\service\impl\UserResumeServiceImpl__Javadoc.json
org\dromara\app\mapper\VideoMapper__Javadoc.json
org\dromara\app\domain\vo\DetailedAbilityReportVo__Javadoc.json
org\dromara\app\domain\InterviewReport$ImprovementSuggestion.class
org\dromara\app\service\tool\impl\CurrentTimeToolExecutor__Javadoc.json
org\dromara\app\domain\LearningProgress$LearningPlan.class
org\dromara\app\domain\vo\AppUserManageVo__Javadoc.json
org\dromara\app\service\IReportGenerationService$RadarChartData__Javadoc.json
org\dromara\app\mapper\AssessmentQuestionOptionMapper.class
META-INF\mps\autoMapper
org\dromara\app\domain\KnowledgeBase__Javadoc.json
org\dromara\app\config\AchievementRabbitMqConfig__Javadoc.json
org\dromara\app\controller\interview\InterviewController$FeedbackRequest__Javadoc.json
org\dromara\app\domain\vo\SensitiveInfoDetectionVo__Javadoc.json
org\dromara\app\config\InterviewConfig$Search__Javadoc.json
org\dromara\app\service\IReportGenerationService$LearningPathRecommendation__Javadoc.json
org\dromara\app\domain\QuestionBookmark__Javadoc.json
org\dromara\app\domain\LearningProgress.class
org\dromara\app\service\ITaskQueueService__Javadoc.json
org\dromara\app\domain\vo\InterviewModeVo__Javadoc.json
org\dromara\app\service\impl\LearningServiceImpl__Javadoc.json
org\dromara\app\service\IInterviewQuestionService$QuestionsByDifficulty.class
org\dromara\app\domain\Video.class
org\dromara\app\domain\vo\InterviewResultResponseVo$SaveHistoryResponse.class
org\dromara\app\mapper\PerformanceMetricsMapper__Javadoc.json
org\dromara\app\service\IToolCallService$ParameterValidationResult__Javadoc.json
org\dromara\app\domain\dto\InterviewReportData$LearningResource__Javadoc.json
org\dromara\app\service\IDataEncryptionService__Javadoc.json
org\dromara\app\service\IAppUserService.class
org\dromara\app\service\ChatMemoryManager$MemoryWrapper__Javadoc.json
org\dromara\app\service\IQuestionManagementService$MultimodalEvaluationConfig.class
org\dromara\app\service\IPaymentSseService__Javadoc.json
org\dromara\app\service\AdvancedRagService$QueryExpansionResult.class
org\dromara\app\domain\vo\DashboardSummaryVO$NextInterviewVO.class
org\dromara\app\event\AchievementEventListener$AbilityImproveEvent__Javadoc.json
org\dromara\app\domain\InterviewResult__Javadoc.json
org\dromara\app\domain\vo\PaymentOrderVo__Javadoc.json
org\dromara\app\mapper\BookReadingRecordMapper__Javadoc.json
org\dromara\app\service\impl\BookReadingRecordServiceImpl__Javadoc.json
org\dromara\app\domain\bo\JobQueryBo.class
org\dromara\app\domain\VideoComment__Javadoc.json
org\dromara\app\domain\vo\InterviewResultResponseVo$QuestionAnalysis.class
org\dromara\app\domain\dto\AppAuthDto__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$TrendData__Javadoc.json
org\dromara\app\service\IAssessmentService__Javadoc.json
org\dromara\app\service\impl\AchievementTriggerService$UserLearnEvent__Javadoc.json
META-INF\spring-configuration-metadata.json
org\dromara\app\event\SimpleAchievementEventListener$LikeEvent__Javadoc.json
org\dromara\app\domain\AssessmentQuestionOption.class
org\dromara\app\config\InterviewConfig$SmartSort__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$InterviewModesResponse__Javadoc.json
org\dromara\app\event\SimpleAchievementEventListener$StudyTimeEvent__Javadoc.json
org\dromara\app\service\tool\ToolExecutor$ValidationResult__Javadoc.json
org\dromara\app\domain\Major__Javadoc.json
org\dromara\app\service\impl\MultimodalAnalysisServiceImpl__Javadoc.json
org\dromara\app\domain\vo\ActivityStatisticsVO__Javadoc.json
org\dromara\app\service\impl\PdfReportServiceImpl__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$CategoriesResponse__Javadoc.json
org\dromara\app\config\InterviewConfig$Device__Javadoc.json
org\dromara\app\service\IOllamaService__Javadoc.json
org\dromara\app\config\AsyncConfig__Javadoc.json
org\dromara\app\controller\learning\LearningController__Javadoc.json
org\dromara\app\domain\vo\InterviewResultResponseVo$InterviewResultSummary.class
org\dromara\app\service\IFileService$FileInfo__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$CreateSessionResponse__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$RelatedJobsResponse__Javadoc.json
org\dromara\app\service\IToolService$ToolCallContext__Javadoc.json
org\dromara\app\domain\vo\AppUserInfoVo__Javadoc.json
org\dromara\app\service\ChatMemoryManager__Javadoc.json
org\dromara\app\service\impl\InterviewResultServiceImpl$UserStatistics__Javadoc.json
org\dromara\app\domain\enums\ErrorCode__Javadoc.json
org\dromara\app\event\SimpleAchievementEventListener$CommentEvent__Javadoc.json
org\dromara\app\service\AdvancedRagService$RetrievalOptions__Javadoc.json
org\dromara\app\domain\vo\JobCategoryVo.class
org\dromara\app\domain\ToolCall.class
org\dromara\app\service\IAchievementService__Javadoc.json
org\dromara\app\domain\KnowledgeDocument__Javadoc.json
org\dromara\app\mapper\QuestionBankBookmarkMapper__Javadoc.json
org\dromara\app\service\IReportGenerationService$InterviewReportData__Javadoc.json
org\dromara\app\service\IKnowledgeService$SearchResult__Javadoc.json
org\dromara\app\service\impl\QuestionCommentServiceImpl__Javadoc.json
org\dromara\app\config\PaymentRabbitMqConfig.class
org\dromara\app\domain\Book__Javadoc.json
org\dromara\app\domain\ChatMessage$MessageMetadata.class
org\dromara\app\utils\VideoMappingUtils__Javadoc.json
org\dromara\app\domain\vo\CacheStatisticsVo__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$SkillPoint__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$DeviceCheckResult__Javadoc.json
org\dromara\app\controller\agent\KnowledgeController__Javadoc.json
org\dromara\app\mapper\JobMapper$JobStatistics__Javadoc.json
org\dromara\app\service\IVideoService__Javadoc.json
org\dromara\app\domain\vo\InterviewResultResponseVo$LearningResource__Javadoc.json
org\dromara\app\mapper\UserAssessmentRecordMapper__Javadoc.json
org\dromara\app\domain\AiTool$ParameterSchema__Javadoc.json
org\dromara\app\service\IOllamaService$OllamaResponse__Javadoc.json
org\dromara\app\service\impl\XunfeiAvatarServiceImpl$AvatarSession__Javadoc.json
org\dromara\app\domain\vo\AchievementStatsVo$LeaderboardInfo__Javadoc.json
org\dromara\app\mapper\ActivityStatisticsMapper__Javadoc.json
org\dromara\app\service\IInterviewAnalysisService__Javadoc.json
org\dromara\app\domain\vo\VideoPurchaseStatusVo__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$InterviewerInfo__Javadoc.json
org\dromara\app\mapper\PaymentOrderMapper__Javadoc.json
org\dromara\app\controller\system\QuestionCommentController__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$JobStatisticsResponse__Javadoc.json
org\dromara\app\config\OllamaConfig$OllamaProperties.class
org\dromara\app\domain\vo\AchievementStatsVo$LeaderboardEntry__Javadoc.json
org\dromara\app\controller\achievement\AchievementController$UnlockRequest__Javadoc.json
org\dromara\app\domain\Job__Javadoc.json
org\dromara\app\controller\achievement\AchievementController$EventRequest__Javadoc.json
org\dromara\app\service\impl\PaymentSseServiceImpl$PaymentCancelledData__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$JobDetailResponse__Javadoc.json
org\dromara\app\service\impl\ChartGenerationServiceImpl__Javadoc.json
org\dromara\app\domain\vo\SmartTaskVO__Javadoc.json
org\dromara\app\domain\dto\ChatRequestDto.class
org\dromara\app\service\impl\InterviewCacheServiceImpl__Javadoc.json
org\dromara\app\service\IToolService$ToolCallResult__Javadoc.json
org\dromara\app\mapper\UserAchievementMapper__Javadoc.json
org\dromara\app\mapper\QuestionTagMapper__Javadoc.json
org\dromara\app\service\IQuestionManagementService.class
org\dromara\app\service\IOllamaService$ServiceStatus.class
org\dromara\app\mapper\VideoShareMapper__Javadoc.json
org\dromara\app\service\IJobService$TechnicalDomainStats__Javadoc.json
org\dromara\app\domain\Question.class
org\dromara\app\service\ConfigurationService.class
org\dromara\app\domain\UserResume.class
org\dromara\app\mapper\JobMapper__Javadoc.json
org\dromara\app\controller\achievement\UserAchievementController__Javadoc.json
org\dromara\app\mapper\AchievementEventMapper__Javadoc.json
org\dromara\app\service\ILearningResourceService__Javadoc.json
org\dromara\app\domain\bo\FeedbackBo.class
org\dromara\app\controller\user\UserResumeController__Javadoc.json
org\dromara\app\domain\vo\AchievementStatsVo__Javadoc.json
org\dromara\app\controller\learning\LearningRecommendationController$PriorityCalculationRequest__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$SuggestionItem__Javadoc.json
org\dromara\app\service\IOllamaService$ServiceStatus__Javadoc.json
org\dromara\app\domain\Job.class
org\dromara\app\service\impl\InterviewResultServiceImpl__Javadoc.json
org\dromara\app\controller\achievement\AchievementEventTestController__Javadoc.json
org\dromara\app\tool\ToolRegistry__Javadoc.json
org\dromara\app\domain\bo\UserResumeBo.class
org\dromara\app\controller\learning\LearningRecommendationController$PersonalizationRequest__Javadoc.json
org\dromara\app\service\AdvancedRagService$SmartRetrievalResult.class
org\dromara\app\mapper\MajorMapper__Javadoc.json
org\dromara\app\service\IQuestionManagementService$DifficultyLevel.class
org\dromara\app\config\SystemPrompts__Javadoc.json
org\dromara\app\controller\agent\ToolController$ParameterValidationRequest__Javadoc.json
org\dromara\app\domain\vo\UserAbilitiesVO__Javadoc.json
org\dromara\app\mapper\QuestionBankMapper__Javadoc.json
org\dromara\app\mapper\QuestionMapper__Javadoc.json
org\dromara\app\domain\Achievement__Javadoc.json
org\dromara\app\controller\achievement\AchievementController__Javadoc.json
org\dromara\app\mapper\VideoLikeMapper.class
org\dromara\app\domain\vo\MajorVo__Javadoc.json
org\dromara\app\service\IXunfeiService$SpeechRecognitionResult__Javadoc.json
org\dromara\app\service\IJobService__Javadoc.json
org\dromara\app\domain\enums\ActivityType.class
org\dromara\app\util\AchievementSystemChecker__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$QuestionResult__Javadoc.json
org\dromara\app\service\tool\ToolExecutor__Javadoc.json
org\dromara\app\service\impl\AssessmentServiceImpl__Javadoc.json
org\dromara\app\service\impl\InterviewResultServiceImpl$InterviewResultData__Javadoc.json
org\dromara\app\exception\InterviewResultExceptionHandler$InterviewResultStatusException__Javadoc.json
org\dromara\app\controller\avatar\XunfeiAvatarController__Javadoc.json
org\dromara\app\domain\VideoCollect__Javadoc.json
org\dromara\app\service\IKnowledgeService__Javadoc.json
org\dromara\app\service\impl\LearningRecommendServiceImpl__Javadoc.json
org\dromara\app\mapper\AchievementMapper__Javadoc.json
org\dromara\app\service\avatar\XunfeiAvatarAuthService.class
org\dromara\app\service\IBookService.class
org\dromara\app\controller\user\UserProfileController__Javadoc.json
org\dromara\app\service\IMultimodalAnalysisService__Javadoc.json
org\dromara\app\service\impl\AdvancedRagServiceImpl__Javadoc.json
org\dromara\app\service\IReportGenerationService$LearningResource__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$ScoreItem__Javadoc.json
org\dromara\app\domain\enums\QuestionDifficultyEnum__Javadoc.json
org\dromara\app\domain\SessionQuestion.class
org\dromara\app\service\IQuestionManagementService$DifficultyGradingSystem__Javadoc.json
org\dromara\app\service\impl\AppUserManageServiceImpl__Javadoc.json
org\dromara\app\domain\vo\UserResumeUploadVo.class
org\dromara\app\domain\vo\VideoLearningStatsVo$CategoryStatsVo__Javadoc.json
org\dromara\app\controller\pay\PaymentSseController__Javadoc.json
org\dromara\app\service\impl\BookServiceImpl__Javadoc.json
org\dromara\app\domain\LearningResource$UsageStatistics__Javadoc.json
org\dromara\app\domain\AssessmentQuestion__Javadoc.json
org\dromara\app\mapper\AgentMapper__Javadoc.json
org\dromara\app\tool\impl\TextProcessorToolExecutor.class
org\dromara\app\domain\vo\avatar\AvatarSessionVo__Javadoc.json
org\dromara\app\domain\ImprovementPlan.class
org\dromara\app\domain\vo\InterviewResponseVo$UserRanking__Javadoc.json
org\dromara\app\mapper\KnowledgeBaseMapper__Javadoc.json
org\dromara\app\mapper\ToolCallMapper.class
org\dromara\app\service\SseConnectionManager$ConnectionStats__Javadoc.json
org\dromara\app\domain\UserAchievement__Javadoc.json
org\dromara\app\domain\vo\BadgeVo__Javadoc.json
org\dromara\app\domain\LearningProgress$ReminderSettings.class
org\dromara\app\mapper\ActivitySummaryMapper__Javadoc.json
org\dromara\app\service\AdvancedRagService.class
org\dromara\app\domain\InterviewMode__Javadoc.json
org\dromara\app\service\impl\DashboardServiceImpl__Javadoc.json
org\dromara\app\config\AppStartupConfiguration.class
org\dromara\app\consumer\PaymentTimeoutConsumer__Javadoc.json
org\dromara\app\domain\LearningResource$QualityAssessment.class
org\dromara\app\controller\learning\LearningProgressController__Javadoc.json
org\dromara\app\domain\vo\FeedbackVo.class
org\dromara\app\controller\interview\ReportController__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$Statistics__Javadoc.json
org\dromara\app\domain\vo\InterviewResultResponseVo$DimensionScore.class
org\dromara\app\event\InterviewEvents$QuestionAnsweredEvent__Javadoc.json
org\dromara\app\service\IMultimodalAnalysisService$AnalysisCallback__Javadoc.json
org\dromara\app\mapper\LearningProgressMapper.class
org\dromara\app\service\impl\AchievementTriggerService$UserShareEvent__Javadoc.json
org\dromara\app\mapper\UserBehaviorMapper.class
org\dromara\app\domain\vo\QuestionCommentVO__Javadoc.json
org\dromara\app\service\ILearningRecommendationService$UserProfile__Javadoc.json
org\dromara\app\controller\agent\ToolController$ToolExecuteRequest__Javadoc.json
org\dromara\app\test_dev\Main.class
org\dromara\app\domain\AiTool$ParameterSchema.class
org\dromara\app\service\PromptEngineeringService$LengthAdjustmentStrategy__Javadoc.json
org\dromara\app\domain\vo\InterviewResultResponseVo$PerformanceMetrics.class
org\dromara\app\domain\vo\QuestionBankVo__Javadoc.json
org\dromara\app\domain\LearningProgress$ReminderSettings__Javadoc.json
org\dromara\app\exception\InterviewException$DeviceCheckFailedException__Javadoc.json
org\dromara\app\domain\LearningProgress$LearningPlan__Javadoc.json
org\dromara\app\mapper\AppUserMapper__Javadoc.json
org\dromara\app\domain\ChatSession__Javadoc.json
org\dromara\app\domain\dto\ChatResponseDto$MessageMetadata.class
org\dromara\app\service\IChatService.class
org\dromara\app\config\PaymentSseConfig__Javadoc.json
org\dromara\app\controller\interview\InterviewController$SubmitAnswerForRoomRequest__Javadoc.json
org\dromara\app\service\IQuestionManagementService$QuestionCoverageAnalysis.class
org\dromara\app\domain\vo\InterviewResponseVo$HistoryListResponse__Javadoc.json
org\dromara\app\service\AdvancedRagService$QueryExpansionResult__Javadoc.json
org\dromara\app\tool\impl\DateTimeToolExecutor__Javadoc.json
org\dromara\app\service\PromptEngineeringService$LengthAdjustmentStrategy.class
org\dromara\app\mapper\VideoMetricsMapper.class
org\dromara\app\domain\vo\VideoCommentVo.class
org\dromara\app\exception\BaseBusinessException.class
org\dromara\app\mapper\JobFavoriteMapper__Javadoc.json
org\dromara\app\domain\vo\SensitiveInfoDetectionVo$PersonalInfoItem__Javadoc.json
org\dromara\app\domain\UserResume__Javadoc.json
org\dromara\app\service\impl\OllamaServiceImpl.class
org\dromara\app\mapper\JobMapper.class
org\dromara\app\event\AchievementEventPublisher.class
org\dromara\app\domain\ChatSession.class
org\dromara\app\mapper\InterviewAnswerMapper__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$ModeInfo__Javadoc.json
org\dromara\app\domain\vo\AchievementVo__Javadoc.json
org\dromara\app\domain\QuestionAnalysis.class
org\dromara\app\service\IXunfeiAvatarService__Javadoc.json
org\dromara\app\domain\QuestionBankBookmark__Javadoc.json
org\dromara\app\service\impl\AchievementNotificationServiceImpl__Javadoc.json
org\dromara\app\service\IOllamaService$ChatMessage.class
org\dromara\app\domain\vo\ActivityStatisticsVO$ActivityTypeStatistics__Javadoc.json
org\dromara\app\service\ILearningRecommendService__Javadoc.json
org\dromara\app\service\impl\InterviewQuestionServiceImpl__Javadoc.json
org\dromara\app\service\IToolService$ParsedToolCall__Javadoc.json
org\dromara\app\service\PromptEngineeringService.class
org\dromara\app\domain\QuestionBank.class
org\dromara\app\domain\bo\FeedbackBo__Javadoc.json
org\dromara\app\mapper\QuestionAnalysisMapper.class
org\dromara\app\service\impl\ToolCallServiceImpl__Javadoc.json
org\dromara\app\exception\AgentException__Javadoc.json
org\dromara\app\domain\dto\ActivityEndRequest__Javadoc.json
org\dromara\app\service\impl\ActivityTimerServiceImpl__Javadoc.json
org\dromara\app\domain\vo\QuestionBankDetailVO__Javadoc.json
org\dromara\app\service\IToolCallService__Javadoc.json
org\dromara\app\event\InterviewEvents$InterviewStartedEvent__Javadoc.json
org\dromara\app\mapper\AchievementEventMapper.class
org\dromara\app\domain\vo\InterviewResultResponseVo$VideoMetrics__Javadoc.json
org\dromara\app\domain\VideoLike.class
org\dromara\app\service\ILearningRecommendService.class
org\dromara\app\domain\bo\KnowledgeBaseBo__Javadoc.json
org\dromara\app\engine\AchievementRuleEngine__Javadoc.json
org\dromara\app\config\ToolInitializationConfig__Javadoc.json
org\dromara\app\mapper\InterviewSessionMapper__Javadoc.json
org\dromara\app\service\IDashboardService__Javadoc.json
org\dromara\app\config\XunfeiConfig$EmotionConfig__Javadoc.json
org\dromara\app\domain\vo\VideoListResultVo$VideoItemVo__Javadoc.json
org\dromara\app\exception\BusinessException__Javadoc.json
org\dromara\app\domain\QuestionAnalysis__Javadoc.json
org\dromara\app\mapper\VectorEmbeddingMapper__Javadoc.json
org\dromara\app\exception\InterviewResultExceptionHandler$InterviewResultNotFoundException__Javadoc.json
org\dromara\app\domain\QuestionTag__Javadoc.json
org\dromara\app\mapper\AssessmentQuestionMapper__Javadoc.json
org\dromara\app\domain\InterviewAnswer__Javadoc.json
org\dromara\app\controller\common\DashBoardController__Javadoc.json
org\dromara\app\controller\learning\VideoController__Javadoc.json
org\dromara\app\domain\SearchKeyword__Javadoc.json
org\dromara\app\domain\dto\InterviewReportData$ImprovementSuggestion__Javadoc.json
org\dromara\app\service\IBookChapterService__Javadoc.json
org\dromara\app\config\OllamaConfig$OllamaProperties$SystemPrompts.class
org\dromara\app\service\IJobService$TechnicalDomainStats.class
org\dromara\app\domain\enums\QuestionTypeEnum__Javadoc.json
org\dromara\app\domain\UserBehavior.class
org\dromara\app\service\IChatService__Javadoc.json
org\dromara\app\domain\vo\InterviewResultResponseVo$LearningResource.class
org\dromara\app\domain\vo\InterviewResponseVo$StatisticsResponse__Javadoc.json
org\dromara\app\domain\LearningProgress$PlanAdjustment__Javadoc.json
org\dromara\app\domain\ImprovementPlan$ImprovementArea.class
org\dromara\app\domain\vo\InterviewResultResponseVo$ImprovementArea.class
org\dromara\app\domain\Agent$QuickAction__Javadoc.json
org\dromara\app\domain\vo\RecommendationResponseVo.class
org\dromara\app\service\IMultimodalAnalysisService$OverallAssessment__Javadoc.json
org\dromara\app\service\impl\FileServiceImpl__Javadoc.json
org\dromara\app\domain\ImprovementPlan$ImprovementArea__Javadoc.json
org\dromara\app\service\IOllamaService$OllamaResponse.class
org\dromara\app\domain\vo\InterviewResultResponseVo$InterviewResultDetail__Javadoc.json
org\dromara\app\mapper\VideoMapper.class
org\dromara\app\mapper\JobCategoryMapper__Javadoc.json
org\dromara\app\domain\InterviewResult.class
org\dromara\app\domain\ChatMessage.class
org\dromara\app\service\IInterviewResultService__Javadoc.json
org\dromara\app\controller\achievement\AchievementSystemController__Javadoc.json
org\dromara\app\service\IAppUserService__Javadoc.json
org\dromara\app\domain\PerformanceMetrics__Javadoc.json
org\dromara\app\domain\vo\PracticeVo__Javadoc.json
org\dromara\app\domain\VideoMetrics.class
org\dromara\app\tool\impl\CalculatorToolExecutor__Javadoc.json
org\dromara\app\domain\AchievementEvent.class
org\dromara\app\domain\InterviewQuestion.class
org\dromara\app\service\IAppUserManageService__Javadoc.json
org\dromara\app\domain\dto\ChatRequestDto$MessageAttachment.class
org\dromara\app\domain\AppUser__Javadoc.json
org\dromara\app\service\AdvancedRagService$RetrievalOptions.class
org\dromara\app\domain\VideoProgress__Javadoc.json
org\dromara\app\service\impl\JobServiceImpl.class
org\dromara\app\domain\vo\InterviewResultResponseVo$VideoMetrics.class
org\dromara\app\listener\AchievementMessageListener__Javadoc.json
org\dromara\app\domain\BookChapter__Javadoc.json
org\dromara\app\domain\dto\ActivityPauseRequest__Javadoc.json
org\dromara\app\mapper\ActivitySessionMapper__Javadoc.json
org\dromara\app\controller\agent\MultimodalAnalysisController__Javadoc.json
org\dromara\app\controller\AppUserManageController__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$TrendPoint__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$QuestionInfo__Javadoc.json
org\dromara\app\domain\vo\QuestionVo__Javadoc.json
org\dromara\app\domain\InterviewReport.class
org\dromara\app\domain\vo\VideoPlayRecordVo__Javadoc.json
org\dromara\app\domain\vo\DashboardSummaryVO$NextInterviewVO__Javadoc.json
org\dromara\app\service\impl\AppUserServiceImpl.class
org\dromara\app\service\IToolService$BatchToolCall__Javadoc.json
org\dromara\app\service\IBookService__Javadoc.json
org\dromara\app\domain\vo\VideoLearningStatsVo$CategoryStatsVo.class
org\dromara\app\exception\InterviewResultExceptionHandler$LearningResourceUnavailableException__Javadoc.json
org\dromara\app\websocket\InterviewWebSocketHandler$InterviewContext__Javadoc.json
org\dromara\app\controller\achievement\AchievementController$ShareRequest__Javadoc.json
org\dromara\app\domain\AiTool$ToolConfig__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$SampleQuestionsResponse__Javadoc.json
org\dromara\app\mapper\InterviewResultMapper__Javadoc.json
org\dromara\app\domain\ActivitySummary__Javadoc.json
org\dromara\app\domain\vo\InterviewResponseVo$EndSessionResponse__Javadoc.json
org\dromara\app\domain\vo\AchievementStatsVo$CategoryStats__Javadoc.json
org\dromara\app\domain\InstructorFollow__Javadoc.json
org\dromara\app\service\impl\ReportGenerationServiceImpl__Javadoc.json
org\dromara\app\service\IPdfReportService__Javadoc.json
org\dromara\app\mapper\UserAssessmentResultMapper__Javadoc.json
org\dromara\app\service\IFileService$FileUploadResult__Javadoc.json
org\dromara\app\domain\vo\ResourceVo__Javadoc.json
org\dromara\app\domain\vo\RecommendationResponseVo__Javadoc.json
org\dromara\app\repository\BookChapterRepository__Javadoc.json
org\dromara\app\service\impl\AppUserProfileServiceImpl__Javadoc.json
