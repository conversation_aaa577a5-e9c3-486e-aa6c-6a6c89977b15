package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__105;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.AppUserManageBoToAppUserMapper;
import org.dromara.app.domain.vo.AppUserManageVo;
import org.dromara.app.domain.vo.AppUserManageVoToAppUserMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__105.class,
    uses = {AppUserManageBoToAppUserMapper.class,AppUserManageVoToAppUserMapper.class},
    imports = {}
)
public interface AppUserToAppUserManageVoMapper extends BaseMapper<AppUser, AppUserManageVo> {
}
