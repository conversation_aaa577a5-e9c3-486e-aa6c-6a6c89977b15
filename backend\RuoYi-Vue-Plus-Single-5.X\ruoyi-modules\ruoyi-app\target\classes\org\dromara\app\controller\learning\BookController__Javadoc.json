{"doc": " 面试书籍控制器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.String", "java.lang.String"], "doc": " 分页查询书籍列表\n\n @param pageNum     页码\n @param pageSize    每页大小\n @param category    分类筛选\n @param searchQuery 搜索关键词\n @return 分页结果\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 查询书籍详情\n\n @param id 书籍ID\n @return 书籍详情\n"}, {"name": "getHotBooks", "paramTypes": ["java.lang.Integer"], "doc": " 查询热门书籍列表\n\n @param limit 限制数量\n @return 热门书籍列表\n"}, {"name": "getRecommendedBooks", "paramTypes": ["java.lang.Integer"], "doc": " 查询推荐书籍列表\n\n @param limit 限制数量\n @return 推荐书籍列表\n"}, {"name": "getCategoryStats", "paramTypes": [], "doc": " 查询分类统计信息\n\n @return 分类统计\n"}, {"name": "startReading", "paramTypes": ["java.lang.Long"], "doc": " 开始阅读书籍（增加阅读次数）\n\n @param id 书籍ID\n @return 操作结果\n"}, {"name": "getChapters", "paramTypes": ["java.lang.Long"], "doc": " 查询书籍章节列表\n\n @param bookId 书籍ID\n @return 章节列表\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>nt", "paramTypes": ["java.lang.String"], "doc": " 查询章节内容\n\n @param chapterId 章节ID\n @return 章节内容\n"}, {"name": "getChapterByOrder", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 根据书籍ID和章节序号查询章节\n\n @param bookId       书籍ID\n @param chapterOrder 章节序号\n @return 章节信息\n"}, {"name": "getPreviewChapters", "paramTypes": ["java.lang.Long"], "doc": " 查询试读章节列表\n\n @param bookId 书籍ID\n @return 试读章节列表\n"}, {"name": "add", "paramTypes": ["org.dromara.app.domain.Book"], "doc": " 新增书籍\n\n @param book 书籍信息\n @return 操作结果\n"}, {"name": "edit", "paramTypes": ["org.dromara.app.domain.Book"], "doc": " 修改书籍\n\n @param book 书籍信息\n @return 操作结果\n"}, {"name": "remove", "paramTypes": ["java.util.List"], "doc": " 删除书籍\n\n @param ids 书籍ID列表\n @return 操作结果\n"}, {"name": "updateStatus", "paramTypes": ["java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": " 更新书籍状态\n\n @param id     书籍ID\n @param status 状态\n @return 操作结果\n"}, {"name": "addChapter", "paramTypes": ["org.dromara.app.domain.BookChapter"], "doc": " 新增章节\n\n @param chapter 章节信息\n @return 操作结果\n"}, {"name": "edit<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["org.dromara.app.domain.BookChapter"], "doc": " 修改章节\n\n @param chapter 章节信息\n @return 操作结果\n"}, {"name": "removeChapter", "paramTypes": ["java.lang.String"], "doc": " 删除章节\n\n @param chapterId 章节ID\n @return 操作结果\n"}, {"name": "updateChapterUnlock", "paramTypes": ["java.lang.String", "java.lang.Bo<PERSON>an"], "doc": " 更新章节解锁状态\n\n @param chapterId  章节ID\n @param isUnlocked 是否解锁\n @return 操作结果\n"}, {"name": "getReadingRecord", "paramTypes": ["java.lang.Long"], "doc": " 获取用户的阅读记录\n\n @param bookId 书籍ID\n @return 阅读记录\n"}, {"name": "saveReadingRecord", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Integer", "java.lang.Integer", "java.util.Map"], "doc": " 保存或更新阅读记录\n\n @param bookId              书籍ID\n @param currentChapterId    当前章节ID\n @param currentChapterIndex 当前章节索引\n @param readingProgress     阅读进度\n @param readingSettings     阅读设置\n @return 操作结果\n"}, {"name": "mark<PERSON><PERSON><PERSON>erCompleted", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 标记章节已完成\n\n @param bookId    书籍ID\n @param chapterId 章节ID\n @return 操作结果\n"}, {"name": "getReadingHistory", "paramTypes": ["java.lang.Integer", "java.lang.Integer"], "doc": " 查询用户阅读历史\n\n @param pageNum  页码\n @param pageSize 每页大小\n @return 阅读历史分页\n"}, {"name": "getRecentReading", "paramTypes": ["java.lang.Integer"], "doc": " 查询最近阅读的书籍\n\n @param limit 限制数量\n @return 最近阅读列表\n"}, {"name": "getReadingStats", "paramTypes": [], "doc": " 查询用户阅读统计\n\n @return 阅读统计数据\n"}, {"name": "addReadingTime", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 增加阅读时长\n\n @param bookId      书籍ID\n @param readingTime 阅读时长（分钟）\n @return 操作结果\n"}], "constructors": []}