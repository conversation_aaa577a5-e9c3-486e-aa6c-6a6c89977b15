{"doc": " 面试结果Controller\n\n <AUTHOR>\n @date 2025-07-17\n", "fields": [], "enumConstants": [], "methods": [{"name": "getResultSummary", "paramTypes": ["java.lang.String"], "doc": " 获取面试结果摘要\n"}, {"name": "getResultDetail", "paramTypes": ["java.lang.String"], "doc": " 获取面试结果详情\n"}, {"name": "getPerformanceMetrics", "paramTypes": ["java.lang.String"], "doc": " 获取性能指标\n"}, {"name": "saveToHistory", "paramTypes": ["org.dromara.app.domain.bo.InterviewResultBo.SaveToHistoryRequest"], "doc": " 保存到历史记录\n"}, {"name": "shareResult", "paramTypes": ["org.dromara.app.domain.bo.InterviewResultBo.ShareResultRequest"], "doc": " 分享结果\n"}, {"name": "getImprovementPlan", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 获取提升计划\n"}, {"name": "getLearningResources", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 获取学习资源推荐\n"}, {"name": "getUserResults", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 获取用户面试结果列表\n"}, {"name": "getUserStatistics", "paramTypes": ["java.lang.Long"], "doc": " 获取用户面试统计\n"}, {"name": "generateReport", "paramTypes": ["java.lang.String"], "doc": " 生成面试报告\n"}, {"name": "deleteResult", "paramTypes": ["java.lang.String"], "doc": " 删除面试结果\n"}], "constructors": []}