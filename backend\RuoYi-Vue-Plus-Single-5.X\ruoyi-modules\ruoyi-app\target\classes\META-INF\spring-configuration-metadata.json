{"groups": [{"name": "app.agent", "type": "org.dromara.app.service.ConfigurationService", "sourceType": "org.dromara.app.service.ConfigurationService"}, {"name": "app.interview", "type": "org.dromara.app.config.InterviewConfig", "sourceType": "org.dromara.app.config.InterviewConfig"}, {"name": "app.interview.device", "type": "org.dromara.app.config.InterviewConfig$Device", "sourceType": "org.dromara.app.config.InterviewConfig", "sourceMethod": "getDevice()"}, {"name": "app.interview.search", "type": "org.dromara.app.config.InterviewConfig$Search", "sourceType": "org.dromara.app.config.InterviewConfig", "sourceMethod": "getSearch()"}, {"name": "app.interview.session", "type": "org.dromara.app.config.InterviewConfig$Session", "sourceType": "org.dromara.app.config.InterviewConfig", "sourceMethod": "getSession()"}, {"name": "app.interview.sort", "type": "org.dromara.app.config.InterviewConfig$Sort", "sourceType": "org.dromara.app.config.InterviewConfig", "sourceMethod": "getSort()"}, {"name": "app.interview.sort.smart-sort", "type": "org.dromara.app.config.InterviewConfig$SmartSort", "sourceType": "org.dromara.app.config.InterviewConfig$Sort", "sourceMethod": "getSmartSort()"}, {"name": "ollama", "type": "org.dromara.app.config.OllamaConfig$OllamaProperties", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties"}, {"name": "ollama.system-prompts", "type": "org.dromara.app.config.OllamaConfig$OllamaProperties$SystemPrompts", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties", "sourceMethod": "getSystemPrompts()"}, {"name": "xun<PERSON>i", "type": "org.dromara.app.config.XunfeiConfig", "sourceType": "org.dromara.app.config.XunfeiConfig"}, {"name": "xunfei.avatar", "type": "org.dromara.app.config.XunfeiAvatarConfig", "sourceType": "org.dromara.app.config.XunfeiAvatarConfig"}, {"name": "xunfei.emotion", "type": "org.dromara.app.config.XunfeiConfig$EmotionConfig", "sourceType": "org.dromara.app.config.XunfeiConfig", "sourceMethod": "getEmotion()"}, {"name": "xunfei.spark", "type": "org.dromara.app.config.XunfeiConfig$SparkConfig", "sourceType": "org.dromara.app.config.XunfeiConfig", "sourceMethod": "getSpark()"}, {"name": "xunfei.speech", "type": "org.dromara.app.config.XunfeiConfig$SpeechConfig", "sourceType": "org.dromara.app.config.XunfeiConfig", "sourceMethod": "getSpeech()"}, {"name": "xunfei.tts", "type": "org.dromara.app.config.XunfeiConfig$TtsConfig", "sourceType": "org.dromara.app.config.XunfeiConfig", "sourceMethod": "getTts()"}], "properties": [{"name": "app.agent.all-config", "type": "java.util.Map<java.lang.String,java.lang.Object>", "sourceType": "org.dromara.app.service.ConfigurationService"}, {"name": "app.agent.all-feature-flags", "type": "java.util.Map<java.lang.String,java.lang.Boolean>", "sourceType": "org.dromara.app.service.ConfigurationService"}, {"name": "app.interview.device.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用设备检测", "sourceType": "org.dromara.app.config.InterviewConfig$Device", "defaultValue": true}, {"name": "app.interview.device.mock-failure-rate", "type": "java.lang.Double", "description": "模拟检测失败概率（用于测试）", "sourceType": "org.dromara.app.config.InterviewConfig$Device", "defaultValue": 0.1}, {"name": "app.interview.device.timeout-seconds", "type": "java.lang.Integer", "description": "检测超时时间（秒）", "sourceType": "org.dromara.app.config.InterviewConfig$Device", "defaultValue": 10}, {"name": "app.interview.search.default-suggestion-limit", "type": "java.lang.Integer", "description": "默认搜索建议数量", "sourceType": "org.dromara.app.config.InterviewConfig$Search", "defaultValue": 5}, {"name": "app.interview.search.hot-keyword-limit", "type": "java.lang.Integer", "description": "热门关键词数量", "sourceType": "org.dromara.app.config.InterviewConfig$Search", "defaultValue": 10}, {"name": "app.interview.search.max-keyword-length", "type": "java.lang.Integer", "description": "关键词最大长度", "sourceType": "org.dromara.app.config.InterviewConfig$Search", "defaultValue": 50}, {"name": "app.interview.search.min-keyword-length", "type": "java.lang.Integer", "description": "关键词最小长度", "sourceType": "org.dromara.app.config.InterviewConfig$Search", "defaultValue": 2}, {"name": "app.interview.search.recent-search-limit", "type": "java.lang.Integer", "description": "最近搜索记录数量", "sourceType": "org.dromara.app.config.InterviewConfig$Search", "defaultValue": 5}, {"name": "app.interview.session.cleanup-interval-minutes", "type": "java.lang.Integer", "description": "会话清理间隔（分钟）", "sourceType": "org.dromara.app.config.InterviewConfig$Session", "defaultValue": 30}, {"name": "app.interview.session.default-expiration-hours", "type": "java.lang.Integer", "description": "默认过期时间（小时）", "sourceType": "org.dromara.app.config.InterviewConfig$Session", "defaultValue": 2}, {"name": "app.interview.session.max-sessions-per-user", "type": "java.lang.Integer", "description": "最大会话数量（每个用户）", "sourceType": "org.dromara.app.config.InterviewConfig$Session", "defaultValue": 5}, {"name": "app.interview.sort.default-sort-by", "type": "java.lang.String", "description": "默认排序方式", "sourceType": "org.dromara.app.config.InterviewConfig$Sort", "defaultValue": "smart"}, {"name": "app.interview.sort.default-sort-order", "type": "java.lang.String", "description": "默认排序顺序", "sourceType": "org.dromara.app.config.InterviewConfig$Sort", "defaultValue": "desc"}, {"name": "app.interview.sort.smart-sort.difficulty-weight", "type": "java.lang.Double", "description": "难度权重", "sourceType": "org.dromara.app.config.InterviewConfig$SmartSort", "defaultValue": 0.2}, {"name": "app.interview.sort.smart-sort.duration-weight", "type": "java.lang.Double", "description": "时长权重", "sourceType": "org.dromara.app.config.InterviewConfig$SmartSort", "defaultValue": 0.15}, {"name": "app.interview.sort.smart-sort.hot-weight", "type": "java.lang.Double", "description": "热度权重", "sourceType": "org.dromara.app.config.InterviewConfig$SmartSort", "defaultValue": 0.3}, {"name": "app.interview.sort.smart-sort.optimal-difficulty", "type": "java.lang.Integer", "description": "最佳难度", "sourceType": "org.dromara.app.config.InterviewConfig$SmartSort", "defaultValue": 3}, {"name": "app.interview.sort.smart-sort.optimal-duration", "type": "java.lang.Double", "description": "最佳时长", "sourceType": "org.dromara.app.config.InterviewConfig$SmartSort", "defaultValue": 37.5}, {"name": "app.interview.sort.smart-sort.optimal-pass-rate", "type": "java.lang.Double", "description": "最佳通过率", "sourceType": "org.dromara.app.config.InterviewConfig$SmartSort", "defaultValue": 65}, {"name": "app.interview.sort.smart-sort.optimal-question-count", "type": "java.lang.Double", "description": "最佳题目数量", "sourceType": "org.dromara.app.config.InterviewConfig$SmartSort", "defaultValue": 12.5}, {"name": "app.interview.sort.smart-sort.pass-rate-weight", "type": "java.lang.Double", "description": "通过率权重", "sourceType": "org.dromara.app.config.InterviewConfig$SmartSort", "defaultValue": 0.25}, {"name": "app.interview.sort.smart-sort.question-count-weight", "type": "java.lang.Double", "description": "题目数量权重", "sourceType": "org.dromara.app.config.InterviewConfig$SmartSort", "defaultValue": 0.1}, {"name": "ollama.base-url", "type": "java.lang.String", "description": "Ollama服务器地址", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties", "defaultValue": "http://localhost:11434"}, {"name": "ollama.connect-timeout", "type": "java.lang.Integer", "description": "连接超时时间（秒）", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties", "defaultValue": 30}, {"name": "ollama.default-max-tokens", "type": "java.lang.Integer", "description": "默认最大Token数", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties", "defaultValue": 4096}, {"name": "ollama.default-model", "type": "java.lang.String", "description": "默认模型名称", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties", "defaultValue": "deepseek-r1:1.5b"}, {"name": "ollama.default-temperature", "type": "java.lang.Double", "description": "默认温度参数", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties", "defaultValue": 0.7}, {"name": "ollama.embedding-model", "type": "java.lang.String", "description": "嵌入模型名称", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties", "defaultValue": "mxbai-embed-large"}, {"name": "ollama.enable-health-check", "type": "java.lang.Bo<PERSON>an", "description": "是否启用健康检查", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties", "defaultValue": true}, {"name": "ollama.enable-streaming", "type": "java.lang.Bo<PERSON>an", "description": "是否启用流式响应", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties", "defaultValue": true}, {"name": "ollama.health-check-interval", "type": "java.lang.Integer", "description": "健康检查间隔（秒）", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties", "defaultValue": 60}, {"name": "ollama.read-timeout", "type": "java.lang.Integer", "description": "读取超时时间（秒）", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties", "defaultValue": 300}, {"name": "ollama.retry-count", "type": "java.lang.Integer", "description": "重试次数", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties", "defaultValue": 3}, {"name": "ollama.retry-interval", "type": "java.lang.Long", "description": "重试间隔（毫秒）", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties", "defaultValue": 1000}, {"name": "ollama.similarity-threshold", "type": "java.lang.Double", "description": "相似度阈值", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties", "defaultValue": 0.7}, {"name": "ollama.system-prompts.career-advisor", "type": "java.lang.String", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties$SystemPrompts", "defaultValue": "你是一个职业规划顾问，可以为求职者提供职业发展建议。"}, {"name": "ollama.system-prompts.general", "type": "java.lang.String", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties$SystemPrompts", "defaultValue": "你是一个专业的rjb-sias，可以回答各种问题并提供有用的建议。请用中文回答。"}, {"name": "ollama.system-prompts.interviewer", "type": "java.lang.String", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties$SystemPrompts", "defaultValue": "你是一个专业的面试官，负责进行技术面试。请根据候选人的背景和目标职位提出相关问题。"}, {"name": "ollama.system-prompts.learning-guide", "type": "java.lang.String", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties$SystemPrompts", "defaultValue": "你是一个学习指导师，可以制定学习计划并提供学习建议。"}, {"name": "ollama.system-prompts.mock-interviewer", "type": "java.lang.String", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties$SystemPrompts", "defaultValue": "你是一个模拟面试官，请进行逼真的面试模拟，并在最后给出评价和建议。"}, {"name": "ollama.system-prompts.resume-analyzer", "type": "java.lang.String", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties$SystemPrompts", "defaultValue": "你是一个专业的简历分析师，可以分析简历内容并提供改进建议。"}, {"name": "ollama.system-prompts.skill-assessor", "type": "java.lang.String", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties$SystemPrompts", "defaultValue": "你是一个技能评估专家，可以评估候选人的技术能力和给出建议。"}, {"name": "ollama.vector-dimension", "type": "java.lang.Integer", "description": "向量维度", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties", "defaultValue": 1024}, {"name": "ollama.write-timeout", "type": "java.lang.Integer", "description": "写入超时时间（秒）", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties", "defaultValue": 30}, {"name": "xunfei.avatar.api-key", "type": "java.lang.String", "description": "API Key", "sourceType": "org.dromara.app.config.XunfeiAvatarConfig"}, {"name": "xunfei.avatar.api-secret", "type": "java.lang.String", "description": "API Secret", "sourceType": "org.dromara.app.config.XunfeiAvatarConfig"}, {"name": "xunfei.avatar.app-id", "type": "java.lang.String", "description": "应用ID", "sourceType": "org.dromara.app.config.XunfeiAvatarConfig"}, {"name": "xunfei.avatar.avatar-url", "type": "java.lang.String", "description": "接口地址", "sourceType": "org.dromara.app.config.XunfeiAvatarConfig", "defaultValue": "wss://avatar.cn-huadong-1.xf-yun.com/v1/interact"}, {"name": "xunfei.avatar.bitrate", "type": "java.lang.Integer", "description": "视频码率", "sourceType": "org.dromara.app.config.XunfeiAvatarConfig", "defaultValue": 5000}, {"name": "xunfei.avatar.connect-timeout", "type": "java.lang.Long", "description": "连接超时时间（毫秒）", "sourceType": "org.dromara.app.config.XunfeiAvatarConfig", "defaultValue": 30000}, {"name": "xunfei.avatar.default-avatar-id", "type": "java.lang.String", "description": "默认形象ID", "sourceType": "org.dromara.app.config.XunfeiAvatarConfig", "defaultValue": "110117005"}, {"name": "xunfei.avatar.default-height", "type": "java.lang.Integer", "description": "默认视频高度", "sourceType": "org.dromara.app.config.XunfeiAvatarConfig", "defaultValue": 1280}, {"name": "xunfei.avatar.default-pitch", "type": "java.lang.Integer", "description": "默认语调", "sourceType": "org.dromara.app.config.XunfeiAvatarConfig", "defaultValue": 50}, {"name": "xunfei.avatar.default-scene-id", "type": "java.lang.String", "description": "默认场景ID", "sourceType": "org.dromara.app.config.XunfeiAvatarConfig", "defaultValue": "77213753883627520"}, {"name": "xunfei.avatar.default-speed", "type": "java.lang.Integer", "description": "默认语速", "sourceType": "org.dromara.app.config.XunfeiAvatarConfig", "defaultValue": 50}, {"name": "xunfei.avatar.default-vcn", "type": "java.lang.String", "description": "默认声音ID", "sourceType": "org.dromara.app.config.XunfeiAvatarConfig", "defaultValue": "x4_lingxiaoying_assist"}, {"name": "xunfei.avatar.default-volume", "type": "java.lang.Integer", "description": "默认音量", "sourceType": "org.dromara.app.config.XunfeiAvatarConfig", "defaultValue": 50}, {"name": "xunfei.avatar.default-width", "type": "java.lang.Integer", "description": "默认视频宽度", "sourceType": "org.dromara.app.config.XunfeiAvatarConfig", "defaultValue": 720}, {"name": "xunfei.avatar.enable-alpha", "type": "java.lang.Bo<PERSON>an", "description": "是否开启透明背景", "sourceType": "org.dromara.app.config.XunfeiAvatarConfig", "defaultValue": false}, {"name": "xunfei.avatar.fps", "type": "java.lang.Integer", "description": "视频帧率", "sourceType": "org.dromara.app.config.XunfeiAvatarConfig", "defaultValue": 25}, {"name": "xunfei.avatar.heartbeat-interval", "type": "java.lang.Long", "description": "心跳间隔（毫秒）", "sourceType": "org.dromara.app.config.XunfeiAvatarConfig", "defaultValue": 5000}, {"name": "xunfei.avatar.protocol", "type": "java.lang.String", "description": "视频协议", "sourceType": "org.dromara.app.config.XunfeiAvatarConfig", "defaultValue": "xrtc"}, {"name": "xunfei.emotion.analysis-type", "type": "java.lang.String", "description": "分析类型", "sourceType": "org.dromara.app.config.XunfeiConfig$EmotionConfig", "defaultValue": "emotion"}, {"name": "xunfei.emotion.api-key", "type": "java.lang.String", "description": "API密钥", "sourceType": "org.dromara.app.config.XunfeiConfig$EmotionConfig"}, {"name": "xunfei.emotion.api-secret", "type": "java.lang.String", "description": "API密钥", "sourceType": "org.dromara.app.config.XunfeiConfig$EmotionConfig"}, {"name": "xunfei.emotion.app-id", "type": "java.lang.String", "description": "应用ID", "sourceType": "org.dromara.app.config.XunfeiConfig$EmotionConfig"}, {"name": "xunfei.emotion.base-url", "type": "java.lang.String", "description": "情感分析服务地址", "sourceType": "org.dromara.app.config.XunfeiConfig$EmotionConfig", "defaultValue": "https://api.xf-yun.com/v1/private/s9a3d6d6c"}, {"name": "xunfei.emotion.language", "type": "java.lang.String", "description": "语言类型", "sourceType": "org.dromara.app.config.XunfeiConfig$EmotionConfig", "defaultValue": "zh"}, {"name": "xunfei.spark.api-password", "type": "java.lang.String", "description": "API密码（HTTP调用认证）", "sourceType": "org.dromara.app.config.XunfeiConfig$SparkConfig", "defaultValue": "LUyfIRXanMIDPXCDdYMy:kIrhmhjMUNjjzYihNOfN"}, {"name": "xunfei.spark.base-url", "type": "java.lang.String", "description": "服务地址", "sourceType": "org.dromara.app.config.XunfeiConfig$SparkConfig", "defaultValue": "https://spark-api-open.xf-yun.com/v1/chat/completions"}, {"name": "xunfei.spark.connect-timeout", "type": "java.lang.Integer", "description": "连接超时时间（秒）", "sourceType": "org.dromara.app.config.XunfeiConfig$SparkConfig", "defaultValue": 30}, {"name": "xunfei.spark.max-tokens", "type": "java.lang.Integer", "description": "最大Token数", "sourceType": "org.dromara.app.config.XunfeiConfig$SparkConfig", "defaultValue": 4096}, {"name": "xunfei.spark.model", "type": "java.lang.String", "description": "模型版本", "sourceType": "org.dromara.app.config.XunfeiConfig$SparkConfig", "defaultValue": "generalv3.5"}, {"name": "xunfei.spark.read-timeout", "type": "java.lang.Integer", "description": "读取超时时间（秒）", "sourceType": "org.dromara.app.config.XunfeiConfig$SparkConfig", "defaultValue": 60}, {"name": "xunfei.spark.temperature", "type": "java.lang.Double", "description": "默认温度", "sourceType": "org.dromara.app.config.XunfeiConfig$SparkConfig", "defaultValue": 0.7}, {"name": "xunfei.spark.version", "type": "java.lang.String", "sourceType": "org.dromara.app.config.XunfeiConfig$SparkConfig"}, {"name": "xunfei.speech.api-key", "type": "java.lang.String", "description": "API密钥", "sourceType": "org.dromara.app.config.XunfeiConfig$SpeechConfig"}, {"name": "xunfei.speech.api-secret", "type": "java.lang.String", "description": "API密钥", "sourceType": "org.dromara.app.config.XunfeiConfig$SpeechConfig"}, {"name": "xunfei.speech.app-id", "type": "java.lang.String", "description": "应用ID", "sourceType": "org.dromara.app.config.XunfeiConfig$SpeechConfig"}, {"name": "xunfei.speech.audio-format", "type": "java.lang.String", "description": "音频格式", "sourceType": "org.dromara.app.config.XunfeiConfig$SpeechConfig", "defaultValue": "audio/L16;rate=16000"}, {"name": "xunfei.speech.base-url", "type": "java.lang.String", "description": "语音识别服务地址", "sourceType": "org.dromara.app.config.XunfeiConfig$SpeechConfig", "defaultValue": "https://iat-api.xfyun.cn/v2/iat"}, {"name": "xunfei.speech.enable-number-convert", "type": "java.lang.Bo<PERSON>an", "description": "是否启用数字转换", "sourceType": "org.dromara.app.config.XunfeiConfig$SpeechConfig", "defaultValue": true}, {"name": "xunfei.speech.enable-punctuation", "type": "java.lang.Bo<PERSON>an", "description": "是否启用标点符号", "sourceType": "org.dromara.app.config.XunfeiConfig$SpeechConfig", "defaultValue": true}, {"name": "xunfei.speech.language", "type": "java.lang.String", "description": "语言类型", "sourceType": "org.dromara.app.config.XunfeiConfig$SpeechConfig", "defaultValue": "zh_cn"}, {"name": "xunfei.tts.api-key", "type": "java.lang.String", "description": "API密钥", "sourceType": "org.dromara.app.config.XunfeiConfig$TtsConfig"}, {"name": "xunfei.tts.api-secret", "type": "java.lang.String", "description": "API密钥", "sourceType": "org.dromara.app.config.XunfeiConfig$TtsConfig"}, {"name": "xunfei.tts.app-id", "type": "java.lang.String", "description": "应用ID", "sourceType": "org.dromara.app.config.XunfeiConfig$TtsConfig"}, {"name": "xunfei.tts.audio-format", "type": "java.lang.String", "description": "音频格式", "sourceType": "org.dromara.app.config.XunfeiConfig$TtsConfig", "defaultValue": "lame"}, {"name": "xunfei.tts.base-url", "type": "java.lang.String", "description": "语音合成服务地址", "sourceType": "org.dromara.app.config.XunfeiConfig$TtsConfig", "defaultValue": "https://tts-api.xfyun.cn/v2/tts"}, {"name": "xunfei.tts.pitch", "type": "java.lang.Integer", "description": "音调", "sourceType": "org.dromara.app.config.XunfeiConfig$TtsConfig", "defaultValue": 50}, {"name": "xunfei.tts.speed", "type": "java.lang.Integer", "description": "语速", "sourceType": "org.dromara.app.config.XunfeiConfig$TtsConfig", "defaultValue": 50}, {"name": "xunfei.tts.voice-name", "type": "java.lang.String", "description": "发音人", "sourceType": "org.dromara.app.config.XunfeiConfig$TtsConfig", "defaultValue": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "xunfei.tts.volume", "type": "java.lang.Integer", "description": "音量", "sourceType": "org.dromara.app.config.XunfeiConfig$TtsConfig", "defaultValue": 50}, {"name": "xunfei.spark.api-key", "type": "java.lang.String", "description": "API密钥（已废弃，保留兼容性）", "sourceType": "org.dromara.app.config.XunfeiConfig$SparkConfig", "deprecated": true, "deprecation": {}}, {"name": "xunfei.spark.api-secret", "type": "java.lang.String", "description": "API密钥（已废弃，保留兼容性）", "sourceType": "org.dromara.app.config.XunfeiConfig$SparkConfig", "deprecated": true, "deprecation": {}}, {"name": "xunfei.spark.app-id", "type": "java.lang.String", "description": "应用ID（已废弃，保留兼容性）", "sourceType": "org.dromara.app.config.XunfeiConfig$SparkConfig", "deprecated": true, "deprecation": {}}], "hints": []}