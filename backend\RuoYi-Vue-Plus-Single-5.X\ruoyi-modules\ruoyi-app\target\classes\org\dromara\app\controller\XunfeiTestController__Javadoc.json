{"doc": " 讯飞AI测试控制器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "testChat", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 测试星火大模型聊天（非流式）\n"}, {"name": "testChatStream", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 测试星火大模型流式聊天\n"}, {"name": "getConfig", "paramTypes": [], "doc": " 测试配置信息\n"}, {"name": "healthCheck", "paramTypes": [], "doc": " 健康检查\n"}], "constructors": []}