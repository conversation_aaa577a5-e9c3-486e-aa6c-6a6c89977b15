package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__105;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.Video;
import org.dromara.app.domain.VideoToVideoDetailVoMapper;
import org.dromara.app.utils.VideoMappingUtils;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__105.class,
    uses = {VideoMappingUtils.class,VideoToVideoDetailVoMapper.class},
    imports = {}
)
public interface VideoDetailVoToVideoMapper extends BaseMapper<VideoDetailVo, Video> {
}
