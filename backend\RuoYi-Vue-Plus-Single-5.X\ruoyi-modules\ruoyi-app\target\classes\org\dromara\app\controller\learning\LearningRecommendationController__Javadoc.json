{"doc": " 学习推荐控制器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "generateLearningRecommendations", "paramTypes": ["java.lang.String"], "doc": " 基于面试会话生成学习推荐\n\n @param sessionId 面试会话ID\n @return 学习路径推荐列表\n"}, {"name": "generateRecommendationsByWeaknesses", "paramTypes": ["org.dromara.app.controller.learning.LearningRecommendationController.WeaknessRecommendationRequest"], "doc": " 基于弱项生成学习推荐\n\n @param request 请求参数\n @return 学习路径推荐列表\n"}, {"name": "generateRecommendationsByJob", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 基于岗位生成学习推荐\n\n @param jobPosition 岗位信息\n @param userLevel 用户水平\n @return 学习路径推荐列表\n"}, {"name": "getRecommendedResources", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 获取学习资源推荐\n\n @param skillArea 技能领域\n @param difficulty 难度等级\n @param resourceType 资源类型\n @return 学习资源列表\n"}, {"name": "calculateLearningPriorities", "paramTypes": ["org.dromara.app.controller.learning.LearningRecommendationController.PriorityCalculationRequest"], "doc": " 计算学习优先级\n\n @param request 请求参数\n @return 优先级映射\n"}, {"name": "personalizelearningPaths", "paramTypes": ["org.dromara.app.controller.learning.LearningRecommendationController.PersonalizationRequest"], "doc": " 个性化学习路径调整\n\n @param request 请求参数\n @return 调整后的学习路径\n"}, {"name": "getLearningPathDetail", "paramTypes": ["java.lang.String"], "doc": " 获取学习路径详情\n\n @param pathId 路径ID\n @return 学习路径详情\n"}, {"name": "getPopularLearningPaths", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 获取热门学习路径\n\n @param category 分类\n @param limit 限制数量\n @return 热门学习路径列表\n"}, {"name": "convertToInterviewReport", "paramTypes": ["org.dromara.app.service.IReportGenerationService.InterviewReportData"], "doc": " 转换报告数据为InterviewReport对象\n"}, {"name": "convertToDomainScore", "paramTypes": ["org.dromara.app.service.IReportGenerationService.DimensionScore"], "doc": " 转换维度评分\n"}, {"name": "getPopularPaths", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 获取热门学习路径（模拟数据）\n"}], "constructors": []}