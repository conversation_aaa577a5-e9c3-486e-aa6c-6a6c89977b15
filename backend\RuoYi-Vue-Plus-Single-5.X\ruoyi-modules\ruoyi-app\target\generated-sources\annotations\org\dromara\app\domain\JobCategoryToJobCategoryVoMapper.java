package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__94;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.vo.JobCategoryVo;
import org.dromara.app.domain.vo.JobCategoryVoToJobCategoryMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__94.class,
    uses = {JobCategoryVoToJobCategoryMapper.class},
    imports = {}
)
public interface JobCategoryToJobCategoryVoMapper extends BaseMapper<JobCategory, JobCategoryVo> {
}
