package org.dromara.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.QuestionComment;
import org.dromara.app.domain.bo.QuestionCommentBo;
import org.dromara.app.domain.vo.QuestionCommentVO;
import org.dromara.app.mapper.QuestionCommentMapper;
import org.dromara.app.service.IQuestionCommentService;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 题目评论Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class QuestionCommentServiceImpl extends ServiceImpl<QuestionCommentMapper, QuestionComment> implements IQuestionCommentService {

    private final QuestionCommentMapper baseMapper;

    /**
     * 查询题目评论
     */
    @Override
    public QuestionCommentVO queryById(Long commentId) {
        return baseMapper.selectVoById(commentId);
    }

    /**
     * 查询题目评论列表
     */
    @Override
    public TableDataInfo<QuestionCommentVO> queryPageList(QuestionCommentBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<QuestionComment> lqw = buildQueryWrapper(bo);
        Page<QuestionCommentVO> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询题目评论列表
     */
    @Override
    public List<QuestionCommentVO> queryList(QuestionCommentBo bo) {
        LambdaQueryWrapper<QuestionComment> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<QuestionComment> buildQueryWrapper(QuestionCommentBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<QuestionComment> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getQuestionId() != null, QuestionComment::getQuestionId, bo.getQuestionId());
        lqw.eq(bo.getUserId() != null, QuestionComment::getUserId, bo.getUserId());
        lqw.eq(bo.getParentId() != null, QuestionComment::getParentId, bo.getParentId());
        lqw.like(StringUtils.isNotBlank(bo.getContent()), QuestionComment::getContent, bo.getContent());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), QuestionComment::getStatus, bo.getStatus());
        lqw.orderByDesc(QuestionComment::getCreateTime);
        return lqw;
    }

    /**
     * 新增题目评论
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(QuestionCommentBo bo) {
        QuestionComment add = BeanUtil.toBean(bo, QuestionComment.class);
        validEntityBeforeSave(add);

        // 设置默认值
        if (add.getLikeCount() == null) {
            add.setLikeCount(0);
        }
        if (add.getReplyCount() == null) {
            add.setReplyCount(0);
        }
        if (add.getStatus() == null) {
            add.setStatus("0");
        }
        if (add.getSort() == null) {
            add.setSort(0);
        }

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setCommentId(add.getCommentId());

            // 如果是回复，更新父评论的回复数
            if (add.getParentId() != null && add.getParentId() > 0) {
                baseMapper.incrementReplyCount(add.getParentId());
            }

            // 更新题目评论数
            baseMapper.updateQuestionCommentCount(add.getQuestionId(), 1);
        }
        return flag;
    }

    /**
     * 修改题目评论
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(QuestionCommentBo bo) {
        QuestionComment update = BeanUtil.toBean(bo, QuestionComment.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(QuestionComment entity) {
        if (entity.getQuestionId() == null) {
            throw new RuntimeException("题目ID不能为空");
        }
        if (entity.getUserId() == null) {
            throw new RuntimeException("用户ID不能为空");
        }
        if (StringUtils.isBlank(entity.getContent())) {
            throw new RuntimeException("评论内容不能为空");
        }
        if (entity.getContent().length() > 1000) {
            throw new RuntimeException("评论内容不能超过1000个字符");
        }
    }

    /**
     * 批量删除题目评论
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids) {
        if (ObjectUtil.isEmpty(ids)) {
            return false;
        }

        // 查询要删除的评论
        List<QuestionComment> comments = baseMapper.selectByIds(ids);
        if (comments.isEmpty()) {
            return false;
        }

        // 统计每个题目的评论数变化
        Map<Long, Integer> questionCommentChanges = new HashMap<>();

        for (QuestionComment comment : comments) {
            // 统计题目评论数变化
            questionCommentChanges.merge(comment.getQuestionId(), -1, Integer::sum);

            // 如果是回复，减少父评论的回复数
            if (comment.getParentId() != null && comment.getParentId() > 0) {
                baseMapper.decrementReplyCount(comment.getParentId());
            }
        }

        // 批量删除评论
        boolean flag = baseMapper.deleteByIds(ids) > 0;

        if (flag) {
            // 更新题目评论数
            for (Map.Entry<Long, Integer> entry : questionCommentChanges.entrySet()) {
                baseMapper.updateQuestionCommentCount(entry.getKey(), entry.getValue());
            }
        }

        return flag;
    }

    /**
     * 获取题目评论列表（包含回复）
     */
    @Override
    public Map<String, Object> getQuestionComments(String questionId, Integer page, Integer pageSize,
                                                   String orderBy, String orderDirection) {
        try {
            Long qId = Long.parseLong(questionId);

            // 设置默认值
            if (page == null || page < 1) page = 1;
            if (pageSize == null || pageSize < 1) pageSize = 10;
            if (StringUtils.isBlank(orderBy)) orderBy = "createTime";
            if (StringUtils.isBlank(orderDirection)) orderDirection = "desc";

            // 创建分页对象
            Page<QuestionCommentVO> pageObj = new Page<>(page, pageSize);

            // 查询主评论列表
            List<QuestionCommentVO> comments = baseMapper.selectCommentListWithReplies(
                pageObj, qId, orderBy, orderDirection);

            // 为每个主评论加载回复
            for (QuestionCommentVO comment : comments) {
                if (comment.getReplyCount() != null && comment.getReplyCount() > 0) {
                    List<QuestionCommentVO> replies = baseMapper.selectRepliesByParentId(
                        Long.parseLong(comment.getId()));
                    comment.setReplies(replies);
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("comments", comments);
            result.put("total", pageObj.getTotal());
            result.put("page", page);
            result.put("pageSize", pageSize);
            result.put("hasMore", page * pageSize < pageObj.getTotal());

            return result;
        } catch (Exception e) {
            log.error("获取题目评论列表失败：{}", e.getMessage(), e);
            throw new RuntimeException("获取评论列表失败");
        }
    }

    /**
     * 创建题目评论
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public QuestionCommentVO createQuestionComment(Long userId, String questionId, String content, Long parentId) {
        try {
            Long qId = Long.parseLong(questionId);

            // 创建评论对象
            QuestionComment comment = new QuestionComment();
            comment.setQuestionId(qId);
            comment.setUserId(userId);
            comment.setContent(content);
            comment.setParentId(parentId);
            comment.setLikeCount(0);
            comment.setReplyCount(0);
            comment.setStatus("0");
            comment.setSort(0);

            // 保存评论
            validEntityBeforeSave(comment);
            boolean flag = baseMapper.insert(comment) > 0;

            if (flag) {
                // 如果是回复，更新父评论的回复数
                if (parentId != null && parentId > 0) {
                    baseMapper.incrementReplyCount(parentId);
                }

                // 更新题目评论数
                baseMapper.updateQuestionCommentCount(qId, 1);

                // 返回创建的评论信息
                return queryById(comment.getCommentId());
            } else {
                throw new RuntimeException("创建评论失败");
            }
        } catch (NumberFormatException e) {
            throw new RuntimeException("题目ID格式错误");
        } catch (Exception e) {
            log.error("创建题目评论失败：{}", e.getMessage(), e);
            throw new RuntimeException("创建评论失败：" + e.getMessage());
        }
    }

    /**
     * 删除题目评论
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteQuestionComment(Long userId, String commentId) {
        try {
            Long cId = Long.parseLong(commentId);

            // 查询评论信息
            QuestionComment comment = baseMapper.selectById(cId);
            if (comment == null) {
                throw new RuntimeException("评论不存在");
            }

            // 检查权限（只能删除自己的评论）
            if (!comment.getUserId().equals(userId)) {
                throw new RuntimeException("无权限删除此评论");
            }

            // 删除评论
            boolean flag = baseMapper.deleteById(cId) > 0;

            if (flag) {
                // 如果是回复，减少父评论的回复数
                if (comment.getParentId() != null && comment.getParentId() > 0) {
                    baseMapper.decrementReplyCount(comment.getParentId());
                }

                // 更新题目评论数
                baseMapper.updateQuestionCommentCount(comment.getQuestionId(), -1);
            }

            return flag;
        } catch (NumberFormatException e) {
            throw new RuntimeException("评论ID格式错误");
        } catch (Exception e) {
            log.error("删除题目评论失败：{}", e.getMessage(), e);
            throw new RuntimeException("删除评论失败：" + e.getMessage());
        }
    }

    /**
     * 点赞/取消点赞评论
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> likeQuestionComment(Long userId, String commentId) {
        try {
            Long cId = Long.parseLong(commentId);

            // 检查当前点赞状态
            Boolean isLiked = baseMapper.selectLikeStatus(userId, cId);
            if (isLiked == null) {
                isLiked = false;
            }

            Map<String, Object> result = new HashMap<>();

            if (isLiked) {
                // 取消点赞
                baseMapper.deleteLikeRecord(cId, userId);
                baseMapper.decrementLikeCount(cId);
                result.put("isLiked", false);
                result.put("message", "取消点赞成功");
            } else {
                // 点赞
                baseMapper.insertLikeRecord(cId, userId);
                baseMapper.incrementLikeCount(cId);
                result.put("isLiked", true);
                result.put("message", "点赞成功");
            }

            // 获取最新的点赞数
            QuestionComment comment = baseMapper.selectById(cId);
            result.put("likeCount", comment != null ? comment.getLikeCount() : 0);

            return result;
        } catch (NumberFormatException e) {
            throw new RuntimeException("评论ID格式错误");
        } catch (Exception e) {
            log.error("点赞评论失败：{}", e.getMessage(), e);
            throw new RuntimeException("点赞操作失败：" + e.getMessage());
        }
    }

    /**
     * 获取评论的回复列表
     */
    @Override
    public List<QuestionCommentVO> getRepliesByParentId(Long parentId) {
        return baseMapper.selectRepliesByParentId(parentId);
    }

    /**
     * 检查用户是否已点赞评论
     */
    @Override
    public Boolean checkUserLikeStatus(Long userId, Long commentId) {
        Boolean status = baseMapper.selectLikeStatus(userId, commentId);
        return status != null ? status : false;
    }

    /**
     * 批量删除评论（管理员功能）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDeleteComments(List<Long> commentIds, Long operatorId) {
        if (ObjectUtil.isEmpty(commentIds)) {
            return false;
        }

        log.info("管理员 {} 批量删除评论：{}", operatorId, commentIds);
        return deleteWithValidByIds(commentIds);
    }

    /**
     * 审核评论（管理员功能）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean auditComment(Long commentId, String status, Long operatorId, String reason) {
        QuestionComment comment = baseMapper.selectById(commentId);
        if (comment == null) {
            throw new RuntimeException("评论不存在");
        }

        comment.setStatus(status);
        comment.setRemark(reason);

        log.info("管理员 {} 审核评论 {}，状态：{}，原因：{}", operatorId, commentId, status, reason);
        return baseMapper.updateById(comment) > 0;
    }

    /**
     * 置顶/取消置顶评论
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean toggleCommentTop(Long commentId, Boolean isTop, Long operatorId) {
        QuestionComment comment = baseMapper.selectById(commentId);
        if (comment == null) {
            throw new RuntimeException("评论不存在");
        }

        // 使用sort字段表示置顶，置顶评论sort为999，普通评论为0
        comment.setSort(isTop ? 999 : 0);

        log.info("管理员 {} {} 评论 {}", operatorId, isTop ? "置顶" : "取消置顶", commentId);
        return baseMapper.updateById(comment) > 0;
    }

    /**
     * 获取评论统计信息
     */
    @Override
    public Map<String, Object> getCommentStatistics(String questionId) {
        try {
            Long qId = Long.parseLong(questionId);

            LambdaQueryWrapper<QuestionComment> lqw = Wrappers.lambdaQuery();
            lqw.eq(QuestionComment::getQuestionId, qId);
            lqw.eq(QuestionComment::getStatus, "0");

            // 总评论数
            Long totalComments = baseMapper.selectCount(lqw);

            // 主评论数（非回复）
            lqw.isNull(QuestionComment::getParentId);
            Long mainComments = baseMapper.selectCount(lqw);

            // 回复数
            Long replies = totalComments - mainComments;

            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalComments", totalComments);
            statistics.put("mainComments", mainComments);
            statistics.put("replies", replies);

            return statistics;
        } catch (NumberFormatException e) {
            throw new RuntimeException("题目ID格式错误");
        }
    }

    /**
     * 搜索评论
     */
    @Override
    public TableDataInfo<QuestionCommentVO> searchComments(String keyword, PageQuery pageQuery) {
        LambdaQueryWrapper<QuestionComment> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(keyword), QuestionComment::getContent, keyword);
        lqw.eq(QuestionComment::getStatus, "0");
        lqw.orderByDesc(QuestionComment::getCreateTime);

        Page<QuestionCommentVO> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 举报评论
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean reportComment(Long userId, String commentId, String reason, String description) {
        try {
            Long cId = Long.parseLong(commentId);

            // 检查评论是否存在
            QuestionComment comment = baseMapper.selectById(cId);
            if (comment == null) {
                throw new RuntimeException("评论不存在");
            }

            // TODO: 这里应该插入到举报表中，暂时记录日志
            log.warn("用户 {} 举报评论 {}，原因：{}，描述：{}", userId, commentId, reason, description);

            return true;
        } catch (NumberFormatException e) {
            throw new RuntimeException("评论ID格式错误");
        } catch (Exception e) {
            log.error("举报评论失败：{}", e.getMessage(), e);
            throw new RuntimeException("举报失败：" + e.getMessage());
        }
    }

    /**
     * 导出评论列表
     */
    @Override
    public List<QuestionCommentVO> exportCommentList(QuestionCommentBo bo) {
        LambdaQueryWrapper<QuestionComment> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }
}
