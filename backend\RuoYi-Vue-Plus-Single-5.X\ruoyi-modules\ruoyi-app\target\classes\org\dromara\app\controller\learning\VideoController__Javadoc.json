{"doc": " 视频课程控制器\n\n @Author: SevenJL\n @CreateTime: 2025-01-05\n @Version: 1.0\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.app.domain.bo.VideoQueryBo"], "doc": " 查询视频课程列表\n"}, {"name": "getHotVideos", "paramTypes": ["java.lang.Integer"], "doc": " 获取热门视频\n"}, {"name": "getDetail", "paramTypes": ["java.lang.Long"], "doc": " 获取视频详情\n"}, {"name": "getLearningStats", "paramTypes": [], "doc": " 获取学习统计数据\n"}, {"name": "getBookmarkedVideos", "paramTypes": ["org.dromara.app.domain.bo.VideoQueryBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取收藏的视频\n"}, {"name": "getPurchasedVideos", "paramTypes": ["org.dromara.app.domain.bo.VideoQueryBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取已购买的视频\n"}, {"name": "getLearningHistory", "paramTypes": ["org.dromara.app.domain.bo.VideoQueryBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取学习历史\n"}, {"name": "savePlayRecord", "paramTypes": ["java.lang.Long"], "doc": " 保存视频播放记录\n\n @param videoId 视频ID\n"}, {"name": "getRelatedVideos", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 获取相关推荐视频\n\n @param id    视频ID\n @param limit 返回数量限制\n"}, {"name": "toggleLike", "paramTypes": ["java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": " 切换视频点赞状态\n"}, {"name": "toggleCollect", "paramTypes": ["java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": " 切换视频收藏状态\n"}, {"name": "shareVideo", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 分享视频\n"}, {"name": "incrementView", "paramTypes": ["java.lang.Long"], "doc": " 增加视频播放次数\n"}, {"name": "updateProgress", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer"], "doc": " 更新视频播放进度\n"}, {"name": "getPlayRecord", "paramTypes": ["java.lang.Long"], "doc": " 获取视频播放记录\n"}, {"name": "checkPurchaseStatus", "paramTypes": ["java.lang.Long"], "doc": " 检查视频购买状态\n"}, {"name": "to<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": " 关注/取关讲师\n"}, {"name": "getComments", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.bo.CommentQueryBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取视频评论列表\n"}, {"name": "publishComment", "paramTypes": ["org.dromara.app.domain.bo.VideoCommentBo"], "doc": " 发布视频评论\n"}, {"name": "toggleCommentLike", "paramTypes": ["java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": " 切换评论点赞状态\n"}, {"name": "uploadVideo", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 上传视频文件\n"}, {"name": "uploadThumbnail", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 上传视频缩略图\n"}, {"name": "isVideoFile", "paramTypes": ["java.lang.String"], "doc": " 检查是否为视频文件\n"}, {"name": "isImageFile", "paramTypes": ["java.lang.String"], "doc": " 检查是否为图片文件\n"}], "constructors": []}