{"doc": " 岗位管理控制器\n\n <AUTHOR> Assistant\n @date 2025-07-19\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.app.domain.bo.JobQueryBo"], "doc": " 分页查询岗位列表\n"}, {"name": "getJobsByDomain", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": " 根据技术领域查询岗位\n"}, {"name": "getJobDetail", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 获取岗位详情\n"}, {"name": "getRecommendedJobs", "paramTypes": ["java.lang.Integer", "java.lang.Long"], "doc": " 获取推荐岗位\n"}, {"name": "getHotJobs", "paramTypes": ["java.lang.Integer", "java.lang.Long"], "doc": " 获取热门岗位\n"}, {"name": "getTechnicalDomainStats", "paramTypes": [], "doc": " 获取技术领域统计\n"}, {"name": "getJobQuestions", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer"], "doc": " 根据岗位获取面试问题\n"}, {"name": "getDomainQuestions", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": " 根据技术领域获取面试问题\n"}, {"name": "getMultimodalQuestions", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 获取多模态面试问题\n"}, {"name": "getQuestionsByTags", "paramTypes": ["java.util.List", "java.lang.Integer"], "doc": " 根据标签获取面试问题\n"}, {"name": "getGradedQuestions", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer", "java.lang.Integer"], "doc": " 获取分级面试问题\n"}], "constructors": []}